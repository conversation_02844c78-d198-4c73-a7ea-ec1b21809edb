import {
  CategoryConfig,
  StatusConfig,
  SubCategoryConfig,
} from '@/constants/complaint';
import { Descriptions, Drawer, Image, Space, Tag, Typography } from 'antd';
import dayjs from 'dayjs';
import React from 'react';

const { Text, Paragraph } = Typography;

interface ComplaintDetailDrawerProps {
  visible: boolean;
  complaint?: API.Complaint;
  onClose: () => void;
}

const ComplaintDetailDrawer: React.FC<ComplaintDetailDrawerProps> = ({
  visible,
  complaint,
  onClose,
}) => {
  if (!complaint) return null;

  /** 获取状态标签 */
  const getStatusTag = (status: API.ComplaintStatus) => {
    const config = StatusConfig[status];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  /** 获取分类标签 */
  const getCategoryTag = (
    category: API.ComplaintCategory,
    subCategory: API.ComplaintSubCategory,
  ) => {
    const categoryInfo = CategoryConfig[category];
    const subCategoryText = SubCategoryConfig[subCategory];

    return (
      <Space>
        <Tag color={categoryInfo.color}>{categoryInfo.text}</Tag>
        <Text>{subCategoryText}</Text>
      </Space>
    );
  };

  return (
    <Drawer
      title="投诉建议详情"
      width={600}
      open={visible}
      onClose={onClose}
      destroyOnClose
    >
      <Descriptions column={1} bordered>
        <Descriptions.Item label="ID">{complaint.id}</Descriptions.Item>

        <Descriptions.Item label="标题">
          <Text strong>{complaint.title}</Text>
        </Descriptions.Item>

        <Descriptions.Item label="分类">
          {getCategoryTag(complaint.category, complaint.subCategory)}
        </Descriptions.Item>

        <Descriptions.Item label="状态">
          {getStatusTag(complaint.status)}
        </Descriptions.Item>

        <Descriptions.Item label="客户信息">
          <Space direction="vertical" size={0}>
            <Text>{complaint.customer?.nickname || '未知客户'}</Text>
            <Text type="secondary">ID: {complaint.customerId}</Text>
            {complaint.customer?.phone && (
              <Text type="secondary">电话: {complaint.customer.phone}</Text>
            )}
          </Space>
        </Descriptions.Item>

        {complaint.orderId && (
          <Descriptions.Item label="关联订单">
            <Space direction="vertical" size={0}>
              <Text>订单ID: {complaint.orderId}</Text>
              {complaint.order?.sn && (
                <Text type="secondary">订单号: {complaint.order.sn}</Text>
              )}
            </Space>
          </Descriptions.Item>
        )}

        {complaint.employeeId && (
          <Descriptions.Item label="关联员工">
            <Space direction="vertical" size={0}>
              <Text>员工ID: {complaint.employeeId}</Text>
              {complaint.employee?.name && (
                <Text type="secondary">姓名: {complaint.employee.name}</Text>
              )}
            </Space>
          </Descriptions.Item>
        )}

        {complaint.contactInfo && (
          <Descriptions.Item label="联系方式">
            {complaint.contactInfo}
          </Descriptions.Item>
        )}

        <Descriptions.Item label="内容">
          <Paragraph>{complaint.content}</Paragraph>
        </Descriptions.Item>

        {complaint.photoURLs && complaint.photoURLs.length > 0 && (
          <Descriptions.Item label="相关图片">
            <Space wrap>
              {complaint.photoURLs.map((url, index) => (
                <Image
                  key={index}
                  width={100}
                  height={100}
                  src={url}
                  style={{ objectFit: 'cover' }}
                  fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
                />
              ))}
            </Space>
          </Descriptions.Item>
        )}

        <Descriptions.Item label="创建时间">
          {dayjs(complaint.createdAt).format('YYYY-MM-DD HH:mm:ss')}
        </Descriptions.Item>

        {complaint.createdBy && (
          <Descriptions.Item label="录入人员">
            <Text>管理员录入 (ID: {complaint.createdBy})</Text>
          </Descriptions.Item>
        )}

        {complaint.adminNote && (
          <Descriptions.Item label="管理员备注">
            <Paragraph>{complaint.adminNote}</Paragraph>
          </Descriptions.Item>
        )}

        {complaint.handlerId && (
          <Descriptions.Item label="处理人员">
            <Space direction="vertical" size={0}>
              <Text>处理人ID: {complaint.handlerId}</Text>
              {complaint.handler?.nickname && (
                <Text type="secondary">姓名: {complaint.handler.nickname}</Text>
              )}
            </Space>
          </Descriptions.Item>
        )}

        {complaint.handledAt && (
          <Descriptions.Item label="处理时间">
            {dayjs(complaint.handledAt).format('YYYY-MM-DD HH:mm:ss')}
          </Descriptions.Item>
        )}

        {complaint.result && (
          <Descriptions.Item label="处理结果">
            <Paragraph>{complaint.result}</Paragraph>
          </Descriptions.Item>
        )}
      </Descriptions>
    </Drawer>
  );
};

export default ComplaintDetailDrawer;

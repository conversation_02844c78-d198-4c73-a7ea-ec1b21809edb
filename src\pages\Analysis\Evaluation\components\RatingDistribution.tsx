import * as reviews from '@/services/reviews';
import { ProCard } from '@ant-design/pro-components';
import { Empty, Progress, message } from 'antd';
import React, { useEffect, useState } from 'react';

interface RatingDistributionData {
  rating: number;
  count: number;
  percentage: number;
}

const RatingDistribution: React.FC = () => {
  const [distributionData, setDistributionData] = useState<
    RatingDistributionData[]
  >([]);
  const [loading, setLoading] = useState(false);

  // 获取评分分布数据
  const fetchDistributionData = async () => {
    setLoading(true);
    try {
      const { errCode, msg, data } = await reviews.ratingDistribution();

      if (errCode) {
        message.error(msg || '获取评分分布数据失败');
        return;
      }

      setDistributionData(data || []);
    } catch (error) {
      console.error('获取评分分布数据失败:', error);
      message.error('获取评分分布数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDistributionData();
  }, []);

  // 渲染分布图
  const renderDistribution = () => {
    if (distributionData.length === 0) {
      return <Empty description="暂无数据" />;
    }

    const colors = ['#ff4d4f', '#ff7a45', '#ffa940', '#52c41a', '#1890ff'];

    return (
      <div style={{ padding: '20px 0' }}>
        {distributionData.map((item, index) => (
          <div key={item.rating} style={{ marginBottom: 16 }}>
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                marginBottom: 4,
              }}
            >
              <span
                style={{
                  width: 60,
                  fontSize: '14px',
                  fontWeight: 500,
                }}
              >
                {item.rating} 星
              </span>
              <div style={{ flex: 1, marginLeft: 12, marginRight: 12 }}>
                <Progress
                  percent={item.percentage}
                  strokeColor={colors[4 - index]}
                  showInfo={false}
                  size="small"
                />
              </div>
              <span
                style={{
                  width: 80,
                  textAlign: 'right',
                  fontSize: '12px',
                  color: '#666',
                }}
              >
                {item.count} 条 ({item.percentage}%)
              </span>
            </div>
          </div>
        ))}

        {/* 总计信息 */}
        <div
          style={{
            marginTop: 20,
            padding: '12px 0',
            borderTop: '1px solid #f0f0f0',
            textAlign: 'center',
            fontSize: '12px',
            color: '#999',
          }}
        >
          总评价数:{' '}
          {distributionData.reduce((sum, item) => sum + item.count, 0)} 条
        </div>
      </div>
    );
  };

  return (
    <ProCard title="评分分布统计" loading={loading} style={{ height: '300px' }}>
      {renderDistribution()}
    </ProCard>
  );
};

export default RatingDistribution;

import Map from '@/components/GaoDeMap';
import { Button, message } from 'antd';
import React, { useRef, useState } from 'react';
import EditTable from './EditTable';

const Vehicle: React.FC = () => {
  const [markers, setMarkers] = useState<Marker[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const mapRef = useRef<any>(null);

  const setActiveMarkers = (id: number) => {
    const newList = markers.map((item) => {
      if (item.id === id) {
        return {
          ...item,
          actived: !item.actived,
        };
      }
      return {
        ...item,
        actived: false,
      };
    });
    console.log(newList);
    setMarkers(newList);
  };

  // 小区搜索功能
  const searchCommunity = () => {
    if (!searchValue.trim() || !mapRef.current) {
      message.warning('请输入小区名称');
      return;
    }

    try {
      // 创建POI搜索实例
      const placeSearch = new mapRef.current.AMap.PlaceSearch({
        pageSize: 5, // 每页结果数
        pageIndex: 1, // 页码
        city: '西安', // 兴趣点城市
        citylimit: true, // 是否强制限制在设置的城市内搜索
        autoFitView: true, // 是否自动调整地图视野使绘制的Marker点都处于视口的可见范围
      });

      // 开始搜索
      placeSearch.search(searchValue, (status: string, result: any) => {
        if (status === 'complete' && result.info === 'OK') {
          // 搜索成功，处理结果
          const pois = result.poiList.pois;
          if (pois.length > 0) {
            // 清除现有标记
            mapRef.current.clearMap();

            // 创建新标记点
            const newMarkers = pois.map((poi: any, index: number) => ({
              id: index + 1,
              position: [poi.location.lng, poi.location.lat],
              title: poi.name,
              content: `<div style="padding: 8px;">
                <h4>${poi.name}</h4>
                <p>地址: ${poi.address}</p>
                <p>类型: ${poi.type}</p>
              </div>`,
              actived: index === 0, // 第一个结果默认激活
            }));

            setMarkers(newMarkers);

            // 设置地图中心点为第一个结果
            mapRef.current.setCenter([
              pois[0].location.lng,
              pois[0].location.lat,
            ]);
            mapRef.current.setZoom(18); // 放大到合适级别

            message.success(`找到 ${pois.length} 个结果`);
          } else {
            message.info('未找到相关小区');
          }
        } else {
          // 搜索失败
          message.error('搜索失败，请重试');
          console.error('搜索失败:', result);
        }
      });
    } catch (error) {
      console.error('搜索出错:', error);
      message.error('搜索功能出错，请刷新页面重试');
    }
  };

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 地图区域 */}
      <div style={{ height: '400px', marginBottom: '16px' }}>
        <Map
          city="西安"
          markers={markers}
          zoom={14}
          activedZoom={18}
          events={{
            onClick: (e: any) => {
              console.log(e);
            },
            onMarkerClick: (marker: Marker) => {
              if (marker.id) {
                setActiveMarkers(marker.id as number);
              }
            },
          }}
          onMapCreated={(mapInstance) => {
            mapRef.current = mapInstance;
          }}
          customControls={[
            // 小区搜索控件
            {
              position: 'LT',
              content: (
                <div
                  style={{
                    padding: '10px',
                    backgroundColor: 'white',
                    borderRadius: '4px',
                  }}
                >
                  <h4>小区搜索</h4>
                  <input
                    type="text"
                    placeholder="输入小区名称"
                    value={searchValue}
                    onChange={(e) => setSearchValue(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && searchCommunity()}
                    style={{
                      width: '150px',
                      padding: '5px',
                      marginRight: '5px',
                      border: '1px solid #ccc',
                      borderRadius: '4px',
                    }}
                  />
                  <Button
                    type="primary"
                    onClick={searchCommunity}
                    style={{
                      padding: '5px 10px',
                      backgroundColor: '#1890ff',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      cursor: 'pointer',
                    }}
                  >
                    搜索
                  </Button>
                </div>
              ),
            },
            // 图层控制控件
            {
              position: 'RB',
              content: (
                <div
                  style={{
                    padding: '10px',
                    backgroundColor: 'white',
                    borderRadius: '4px',
                  }}
                >
                  <h4>图层控制</h4>
                  <div>
                    <label style={{ display: 'block', margin: '5px 0' }}>
                      <input
                        type="checkbox"
                        onChange={(e) => {
                          if (mapRef.current) {
                            if (e.target.checked) {
                              if (mapRef.current.satelliteLayer) {
                                mapRef.current.add(
                                  mapRef.current.satelliteLayer,
                                );
                              }
                            } else {
                              if (mapRef.current.satelliteLayer) {
                                mapRef.current.remove(
                                  mapRef.current.satelliteLayer,
                                );
                              }
                            }
                          }
                        }}
                      />{' '}
                      卫星图层
                    </label>
                    <label style={{ display: 'block', margin: '5px 0' }}>
                      <input
                        type="checkbox"
                        onChange={(e) => {
                          if (mapRef.current) {
                            if (e.target.checked) {
                              if (mapRef.current.roadNetLayer) {
                                mapRef.current.add(mapRef.current.roadNetLayer);
                              }
                            } else {
                              if (mapRef.current.roadNetLayer) {
                                mapRef.current.remove(
                                  mapRef.current.roadNetLayer,
                                );
                              }
                            }
                          }
                        }}
                      />{' '}
                      路网图层
                    </label>
                    <label style={{ display: 'block', margin: '5px 0' }}>
                      <input
                        type="checkbox"
                        defaultChecked
                        onChange={(e) => {
                          if (mapRef.current) {
                            if (e.target.checked) {
                              if (mapRef.current.buildingLayer) {
                                mapRef.current.add(
                                  mapRef.current.buildingLayer,
                                );
                              }
                            } else {
                              if (mapRef.current.buildingLayer) {
                                mapRef.current.remove(
                                  mapRef.current.buildingLayer,
                                );
                              }
                            }
                          }
                        }}
                      />{' '}
                      建筑物
                    </label>
                  </div>
                </div>
              ),
            },
          ]}
          style={{ height: '100%' }}
        />
      </div>

      {/* 表格区域 */}
      <div style={{ flex: 1, minHeight: '400px' }}>
        <EditTable
          setMarkers={setMarkers}
          setActiveMarkers={setActiveMarkers}
        />
      </div>
    </div>
  );
};

export default Vehicle;

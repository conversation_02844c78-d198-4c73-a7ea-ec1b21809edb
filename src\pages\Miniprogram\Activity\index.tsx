import {
  create,
  index,
  publish,
  remove,
  unpublish,
  update,
} from '@/services/activity';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, Image, message, Popconfirm, Space, Tag, Tooltip } from 'antd';
import React, { useRef, useState } from 'react';
import EditModal from './EditModal';
import PreviewModal from './PreviewModal';
import PublishModal from './PublishModal';

const ActivityManagement: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [publishModalVisible, setPublishModalVisible] = useState(false);
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [current, setCurrent] = useState<API.Activity | undefined>(undefined);

  const handleSave = async (values: API.Activity) => {
    let response;
    if (current) {
      const { id, createdAt, updatedAt, publishedAt, isPublished, ...info } =
        values;
      void id;
      void createdAt;
      void updatedAt;
      void publishedAt;
      void isPublished;
      response = await update(id, { ...info, target: '用户端' });
    } else {
      const { id, createdAt, updatedAt, publishedAt, isPublished, ...info } =
        values;
      void id;
      void createdAt;
      void updatedAt;
      void publishedAt;
      void isPublished;
      response = await create({ ...info, target: '用户端' });
    }

    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('操作成功');
      actionRef?.current?.reload();
      setEditModalVisible(false);
    }
  };

  const handlePreview = (record: API.Activity) => {
    setCurrent(record);
    setPreviewModalVisible(true);
  };

  const handleDelete = async (record: API.Activity) => {
    if (record.isPublished === 1) {
      message.error('已发布的活动不能删除，请先取消发布');
      return;
    }

    const response = await remove(record.id);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('删除成功');
      actionRef?.current?.reload();
    }
  };

  const handlePublish = async (id: number) => {
    const response = await publish(id);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('发布成功');
      actionRef?.current?.reload();
      setPublishModalVisible(false);
    }
  };

  const handleUnpublish = async (id: number) => {
    const response = await unpublish(id);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('取消发布成功');
      actionRef?.current?.reload();
      setPublishModalVisible(false);
    }
  };

  const columns: ProColumns<API.Activity, 'text'>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      hideInSearch: true,
    },
    {
      title: '活动标题',
      dataIndex: 'title',
      key: 'title',
      width: 200,
      ellipsis: true,
    },

    {
      title: '内容类型',
      dataIndex: 'contentType',
      key: 'contentType',
      width: 100,
      hideInSearch: true,
      render: (_, record) => (
        <Tag color={record.contentType === 'content' ? 'blue' : 'green'}>
          {record.contentType === 'content' ? '富文本' : '链接'}
        </Tag>
      ),
    },
    {
      title: '封面图片',
      dataIndex: 'coverImage',
      key: 'coverImage',
      width: 120,
      hideInSearch: true,
      render: (_, record) => {
        if (!record.coverImage) return '-';
        return (
          <Image
            src={record.coverImage}
            alt=""
            width={80}
            height={60}
            style={{ objectFit: 'cover' }}
          />
        );
      },
    },

    {
      title: '发布状态',
      dataIndex: 'isPublished',
      key: 'isPublished',
      width: 100,
      valueType: 'select',
      valueEnum: {
        0: { text: '未发布', status: 'Default' },
        1: { text: '已发布', status: 'Success' },
      },
      render: (_, record) => (
        <Tag color={record.isPublished === 1 ? 'green' : 'default'}>
          {record.isPublished === 1 ? '已发布' : '未发布'}
        </Tag>
      ),
    },

    {
      title: '发布时间',
      dataIndex: 'publishedAt',
      key: 'publishedAt',
      width: 160,
      hideInSearch: true,
      valueType: 'dateTime',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      hideInSearch: true,
      valueType: 'dateTime',
    },
    {
      title: '操作',
      key: 'action',
      valueType: 'option',
      width: 200,
      align: 'center',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            onClick={() => handlePreview(record)}
          >
            预览
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => {
              setCurrent(record);
              setEditModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => {
              setCurrent(record);
              setPublishModalVisible(true);
            }}
          >
            发布管理
          </Button>
          <Popconfirm
            title="确认删除？"
            description={
              record.isPublished === 1
                ? '已发布的活动不能删除'
                : '删除后不可恢复'
            }
            onConfirm={() => handleDelete(record)}
            disabled={record.isPublished === 1}
          >
            <Tooltip
              title={record.isPublished === 1 ? '已发布的活动不能删除' : ''}
            >
              <Button
                type="link"
                danger
                size="small"
                disabled={record.isPublished === 1}
              >
                删除
              </Button>
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<API.Activity>
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        request={async (params) => {
          const { errCode, msg, data } = await index(params);
          if (errCode) {
            message.error(msg || '列表查询失败');
            return {
              data: [],
              total: 0,
            };
          }
          return {
            data: data?.list || [],
            total: data?.total || 0,
          };
        }}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            onClick={() => {
              setCurrent(undefined);
              setEditModalVisible(true);
            }}
          >
            新增活动
          </Button>,
        ]}
        scroll={{ x: 1200 }}
      />

      <EditModal
        open={editModalVisible}
        info={current}
        onClose={() => setEditModalVisible(false)}
        onSave={handleSave}
      />

      <PublishModal
        open={publishModalVisible}
        activity={current}
        onClose={() => setPublishModalVisible(false)}
        onPublish={handlePublish}
        onUnpublish={handleUnpublish}
      />

      <PreviewModal
        open={previewModalVisible}
        activity={current}
        onClose={() => setPreviewModalVisible(false)}
      />
    </>
  );
};

export default ActivityManagement;

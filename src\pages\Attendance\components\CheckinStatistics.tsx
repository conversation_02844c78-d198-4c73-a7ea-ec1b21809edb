import { employeeCheckins } from '@/services';
import {
  CalendarOutlined,
  TeamOutlined,
  TrophyOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { ProCard, StatisticCard } from '@ant-design/pro-components';
import { DatePicker, message, Spin } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import React, { useEffect, useState } from 'react';

const { RangePicker } = DatePicker;

interface CheckinStatisticsProps {
  onRefresh?: () => void;
}

const CheckinStatistics: React.FC<CheckinStatisticsProps> = () => {
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState<API.EmployeeCheckinStats>();
  const [dateRange, setDateRange] = useState<[Dayjs, Dayjs]>([
    dayjs().subtract(7, 'day'),
    dayjs(),
  ]);

  const fetchStatistics = async () => {
    setLoading(true);
    try {
      const { errCode, msg, data } = await employeeCheckins.getStatistics({
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD'),
      });

      if (errCode) {
        message.error(msg || '获取统计数据失败');
      } else {
        setStatistics(data);
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
      message.error('获取统计数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStatistics();
  }, [dateRange]);

  const handleDateRangeChange = (dates: [Dayjs, Dayjs] | null) => {
    if (dates) {
      setDateRange(dates);
    }
  };

  return (
    <Spin spinning={loading}>
      <ProCard
        title="打卡统计"
        extra={
          <RangePicker
            value={dateRange}
            onChange={handleDateRangeChange}
            allowClear={false}
            format="YYYY-MM-DD"
            placeholder={['开始日期', '结束日期']}
          />
        }
        headerBordered
      >
        <StatisticCard.Group>
          <StatisticCard
            statistic={{
              title: '总打卡次数',
              value: statistics?.totalCount || 0,
              icon: <CalendarOutlined style={{ color: '#1890ff' }} />,
            }}
          />
          <StatisticCard
            statistic={{
              title: '今日打卡次数',
              value: statistics?.todayCount || 0,
              icon: <TrophyOutlined style={{ color: '#52c41a' }} />,
            }}
          />
          <StatisticCard
            statistic={{
              title: '本周打卡次数',
              value: statistics?.weekCount || 0,
              icon: <CalendarOutlined style={{ color: '#faad14' }} />,
            }}
          />
          <StatisticCard
            statistic={{
              title: '本月打卡次数',
              value: statistics?.monthCount || 0,
              icon: <CalendarOutlined style={{ color: '#722ed1' }} />,
            }}
          />
        </StatisticCard.Group>

        <StatisticCard.Group style={{ marginTop: 16 }}>
          <StatisticCard
            statistic={{
              title: '活跃员工数',
              value: statistics?.activeEmployeeCount || 0,
              icon: <TeamOutlined style={{ color: '#13c2c2' }} />,
            }}
          />
          <StatisticCard
            statistic={{
              title: '今日活跃员工数',
              value: statistics?.todayActiveEmployeeCount || 0,
              icon: <UserOutlined style={{ color: '#eb2f96' }} />,
            }}
          />
          <StatisticCard
            statistic={{
              title: '平均每日打卡',
              value: statistics?.totalCount
                ? Math.round(
                    statistics.totalCount /
                      Math.max(1, dateRange[1].diff(dateRange[0], 'day') + 1),
                  )
                : 0,
              icon: <CalendarOutlined style={{ color: '#f5222d' }} />,
            }}
          />
          <StatisticCard
            statistic={{
              title: '员工平均打卡',
              value:
                statistics?.totalCount && statistics?.activeEmployeeCount
                  ? Math.round(
                      statistics.totalCount / statistics.activeEmployeeCount,
                    )
                  : 0,
              icon: <UserOutlined style={{ color: '#fa8c16' }} />,
            }}
          />
        </StatisticCard.Group>
      </ProCard>
    </Spin>
  );
};

export default CheckinStatistics;

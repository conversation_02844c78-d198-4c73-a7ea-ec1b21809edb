import { Pie } from '@ant-design/plots';
import { Empty } from 'antd';
import React from 'react';

export interface PieChartData {
  type: string;
  value: number;
  percentage?: number;
}

interface PieChartProps {
  data: PieChartData[];
  height?: number;
  colors?: string[];
  showStatistic?: boolean;
  statisticTitle?: string;
  valueUnit?: string;
  labelPosition?: 'inner' | 'outer' | 'spider';
  emptyText?: string;
}

const PieChart: React.FC<PieChartProps> = ({
  data,
  height = 300,
  colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#13c2c2'],
  showStatistic = false,
  statisticTitle = '总计',
  valueUnit = '',
  labelPosition = 'spider',
  emptyText = '暂无数据',
}) => {
  if (!data || data.length === 0) {
    return <Empty description={emptyText} />;
  }

  const config = {
    data,
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    height,
    label: {
      text: (d: any) => {
        if (labelPosition === 'spider') {
          return `${d.type}\n${d.value}${valueUnit} (${(
            d.percentage || 0
          ).toFixed(1)}%)`;
        } else if (labelPosition === 'inner') {
          return `${d.value}${valueUnit}`;
        } else {
          return `${d.type} ${d.value}${valueUnit}`;
        }
      },
      position: labelPosition,
    },
    legend: {
      color: {
        title: false,
        position: 'bottom',
        rowPadding: 5,
      },
    },
    color: colors,
    tooltip: {
      title: (d: any) => d.type,
      items: [
        {
          field: 'value',
          name: '数量',
          valueFormatter: (value: any) => `${value}${valueUnit}`,
        },
        {
          field: 'percentage',
          name: '占比',
          valueFormatter: (value: any) => `${(value || 0).toFixed(1)}%`,
        },
      ],
    },
    interactions: [
      {
        type: 'element-active',
      },
    ],
    ...(showStatistic && {
      statistic: {
        title: {
          style: {
            fontSize: '14px',
            color: '#8c8c8c',
          },
          content: statisticTitle,
        },
        content: {
          style: {
            fontSize: '18px',
            fontWeight: 'bold',
            color: '#262626',
          },
          formatter: () => {
            const total = data.reduce((sum, item) => sum + item.value, 0);
            return `${total}${valueUnit}`;
          },
        },
      },
    }),
  };

  return <Pie {...config} />;
};

export default PieChart;

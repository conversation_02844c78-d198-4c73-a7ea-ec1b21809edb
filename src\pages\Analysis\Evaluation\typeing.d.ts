// 统计数据接口
interface StatisticsData {
  totalReviews: number;
  todayReviews: number;
  averageRating: number;
  positiveRate: number;
  thisWeekReviews: number;
  thisMonthReviews: number;
}

// 评价列表数据接口
interface ReviewData {
  id: number;
  orderId: number;
  rating: number;
  comment?: string;
  photoURLs?: string[];
  createdAt?: string;
  order?: {
    sn: string;
    employee?: API.Employee;
  };
  customer?: API.Customer;
}

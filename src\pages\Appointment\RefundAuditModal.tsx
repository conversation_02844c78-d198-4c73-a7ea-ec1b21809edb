import { auditRefund } from '@/services/order';
import {
  Button,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Radio,
  Space,
  Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';

type RefundAuditModalProps = {
  open: boolean;
  current?: API.Order;
  onClose: () => void;
  onSuccess: () => void;
};

const RefundAuditModal: React.FC<RefundAuditModalProps> = ({
  open,
  current,
  onClose,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [result, setResult] = useState<boolean>(true);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (open && current) {
      form.setFieldsValue({
        result: true,
        money: current.totalFee,
      });
      setResult(true);
    } else {
      form.resetFields();
    }
  }, [open, current, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      const response = await auditRefund(current?.sn || '', {
        result: values.result,
        reason: values.reason,
        money: values.money,
      });

      if (response.errCode) {
        message.error(response.msg);
      } else {
        message.success('审核成功');
        onSuccess();
        onClose();
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePercentageClick = (percentage: number) => {
    const totalFee = current?.totalFee || 0;
    form.setFieldsValue({ money: (totalFee * percentage) / 100 });
  };

  return (
    <Modal
      title="退款审核"
      open={open}
      onCancel={onClose}
      footer={[
        <Button key="cancel" onClick={onClose}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={handleSubmit}
        >
          确定
        </Button>,
      ]}
    >
      <Typography.Title level={5}>
        {current?.orderDetails?.[0]?.service?.serviceName || '未知服务'}
      </Typography.Title>
      <Typography.Text>
        订单金额: ¥{current?.totalFee || '0.00'}
      </Typography.Text>
      <br />
      <Typography.Text>
        申请时间:{' '}
        {current?.updatedAt
          ? new Date(current.updatedAt).toLocaleString()
          : '未知'}
      </Typography.Text>

      <Form form={form} layout="vertical" style={{ marginTop: 20 }}>
        <Form.Item
          name="result"
          label="审核结果"
          rules={[{ required: true, message: '请选择审核结果' }]}
        >
          <Radio.Group onChange={(e) => setResult(e.target.value)}>
            <Radio value={true}>同意</Radio>
            <Radio value={false}>不同意</Radio>
          </Radio.Group>
        </Form.Item>

        {result ? (
          <>
            <Form.Item
              name="money"
              label="退款金额"
              rules={[{ required: true, message: '请输入退款金额' }]}
            >
              <InputNumber
                style={{ width: '100%' }}
                min={0}
                max={current?.totalFee}
                precision={2}
                addonBefore="¥"
              />
            </Form.Item>
            <Form.Item>
              <Space>
                <Button size="small" onClick={() => handlePercentageClick(100)}>
                  全额
                </Button>
                <Button size="small" onClick={() => handlePercentageClick(80)}>
                  80%
                </Button>
                <Button size="small" onClick={() => handlePercentageClick(60)}>
                  60%
                </Button>
                <Button size="small" onClick={() => handlePercentageClick(40)}>
                  40%
                </Button>
                <Button size="small" onClick={() => handlePercentageClick(20)}>
                  20%
                </Button>
              </Space>
            </Form.Item>
          </>
        ) : (
          <Form.Item
            name="reason"
            label="驳回原因"
            rules={[{ required: true, message: '请输入驳回原因' }]}
          >
            <Input.TextArea rows={4} maxLength={200} showCount />
          </Form.Item>
        )}
      </Form>
    </Modal>
  );
};

export default RefundAuditModal;

import {
  ModalForm,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Form } from 'antd';
import React, { useEffect } from 'react';

type EditModalProps = {
  open: boolean;
  info?: API.Role;
  onSave: (info: API.Role) => Promise<void>;
  onClose: () => void;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
}) => {
  const [form] = Form.useForm();

  // 当模态框打开时重置表单
  useEffect(() => {
    if (open) {
      if (info) {
        // 编辑模式：设置表单值
        form.setFieldsValue(info);
      } else {
        // 新增模式：重置表单
        form.resetFields();
      }
    }
  }, [open, info, form]);

  return (
    <ModalForm<API.Role>
      title={info ? '编辑角色' : '注册角色'}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
      }}
      open={open}
      layout="horizontal"
      grid
      labelCol={{ flex: '7em' }}
      onFinish={onSave}
      form={form}
    >
      <ProFormText name="id" label="ID" hidden />
      <ProFormText
        name="name"
        label="名称"
        rules={[{ required: true, message: '请输入名称！' }]}
        colProps={{ span: 12 }}
      />
      <ProFormTextArea
        name="description"
        label="描述"
        fieldProps={{ maxLength: 100, showCount: true }}
      />
    </ModalForm>
  );
};

export default EditModal;

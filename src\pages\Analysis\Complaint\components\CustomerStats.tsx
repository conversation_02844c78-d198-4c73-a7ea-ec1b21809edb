import * as complaints from '@/services/complaints';
import { ProCard, ProColumns, ProTable } from '@ant-design/pro-components';
import { Avatar, message, Progress, Space, Tag, Typography } from 'antd';
import { Dayjs } from 'dayjs';
import React from 'react';

const { Text } = Typography;

interface CustomerStatsProps {
  dateRange: [Dayjs, Dayjs];
}

const CustomerStats: React.FC<CustomerStatsProps> = ({ dateRange }) => {
  // const [loading, setLoading] = useState(false);
  // const [data, setData] = useState<API.ComplaintCustomerStats[]>([]);
  // const [total, setTotal] = useState(0);

  const fetchData = async (params: any) => {
    // setLoading(true);
    try {
      const {
        errCode,
        msg,
        data: responseData,
      } = await complaints.customerStats({
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD'),
        page: params.current,
        pageSize: params.pageSize,
        sortBy: params.sortBy || 'totalCount',
        sortOrder: params.sortOrder || 'desc',
      });

      if (errCode) {
        message.error(msg || '获取客户统计数据失败');
        return {
          data: [],
          total: 0,
          success: false,
        };
      }

      if (responseData) {
        // setData(responseData.list);
        // setTotal(responseData.total);
        return {
          data: responseData.list,
          total: responseData.total,
          success: true,
        };
      }

      return {
        data: [],
        total: 0,
        success: false,
      };
    } catch (error) {
      console.error('获取客户统计数据失败:', error);
      message.error('获取客户统计数据失败');
      return {
        data: [],
        total: 0,
        success: false,
      };
    } finally {
      // setLoading(false);
    }
  };

  const columns: ProColumns<API.ComplaintCustomerStats>[] = [
    {
      title: '客户信息',
      dataIndex: 'customerName',
      width: 200,
      render: (_: any, record: API.ComplaintCustomerStats) => (
        <Space>
          <Avatar src={record.customerAvatar} size="small">
            {record.customerName?.charAt(0)}
          </Avatar>
          <div>
            <div>{record.customerName || '未知客户'}</div>
            <Text type="secondary" style={{ fontSize: 12 }}>
              {record.customerPhone}
            </Text>
          </div>
        </Space>
      ),
    },
    {
      title: '会员状态',
      dataIndex: 'memberStatus',
      width: 100,
      render: (_status, entity) => (
        <Tag color={entity.memberStatus === 1 ? 'gold' : 'default'}>
          {entity.memberStatus === 1 ? '权益会员' : '普通会员'}
        </Tag>
      ),
    },
    {
      title: '总投诉建议数',
      dataIndex: 'totalCount',
      width: 120,
      sorter: true,
      render: (count) => (
        <Text strong style={{ color: '#1890ff' }}>
          {count}
        </Text>
      ),
    },
    {
      title: '投诉数量',
      dataIndex: 'complaintCount',
      width: 100,
      sorter: true,
      render: (count) => <Text style={{ color: '#ff4d4f' }}>{count}</Text>,
    },
    {
      title: '建议数量',
      dataIndex: 'suggestionCount',
      width: 100,
      sorter: true,
      render: (count) => <Text style={{ color: '#52c41a' }}>{count}</Text>,
    },
    {
      title: '已解决数量',
      dataIndex: 'resolvedCount',
      width: 100,
      render: (count) => <Text style={{ color: '#722ed1' }}>{count}</Text>,
    },
    {
      title: '解决率',
      dataIndex: 'resolveRate',
      width: 150,
      sorter: true,
      render: (_rate, record) => (
        <div style={{ width: 120 }}>
          <Progress
            percent={record.resolveRate}
            size="small"
            strokeColor={
              record.resolveRate >= 80
                ? '#52c41a'
                : record.resolveRate >= 60
                ? '#faad14'
                : '#ff4d4f'
            }
            format={(percent) => `${percent?.toFixed(1)}%`}
          />
        </div>
      ),
    },
  ];

  return (
    <ProCard title="客户投诉建议统计">
      <ProTable<API.ComplaintCustomerStats>
        columns={columns}
        request={fetchData}
        rowKey="customerId"
        search={false}
        pagination={{
          defaultPageSize: 20,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
        }}
        scroll={{ x: 800 }}
        size="small"
        options={{
          reload: true,
          density: false,
          setting: false,
        }}
        params={{ dateRange }}
      />
    </ProCard>
  );
};

export default CustomerStats;

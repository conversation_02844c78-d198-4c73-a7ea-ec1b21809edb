import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { message } from 'antd';
import moment from 'moment';
import React, { useRef } from 'react';

interface UsageRecordListProps {
  customerCouponId: number;
}

/**
 * 代金券使用记录列表组件
 */
const UsageRecordList: React.FC<UsageRecordListProps> = ({
  customerCouponId,
}) => {
  const actionRef = useRef<ActionType>();

  // 表格列定义
  const columns: ProColumns<API.CouponUsageRecord>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      search: false,
    },
    {
      title: '使用时间',
      dataIndex: 'useTime',
      width: 180,
      valueType: 'dateTime',
      search: false,
      render: (_, record) =>
        record.useTime
          ? moment(record.useTime).format('YYYY-MM-DD HH:mm:ss')
          : '-',
    },
    {
      title: '订单ID',
      dataIndex: 'orderId',
      width: 100,
      search: false,
    },
    {
      title: '订单编号',
      dataIndex: ['order', 'sn'],
      width: 180,
      search: false,
    },
    {
      title: '订单金额',
      dataIndex: ['order', 'totalFee'],
      width: 120,
      valueType: 'money',
      search: false,
    },
  ];

  return (
    <ProTable<API.CouponUsageRecord>
      actionRef={actionRef}
      rowKey="id"
      columns={columns}
      search={false}
      pagination={{
        pageSize: 10,
        showSizeChanger: true,
      }}
      options={false}
      request={async () => {
        if (!customerCouponId) {
          return {
            data: [],
            success: true,
            total: 0,
          };
        }

        try {
          // TODO: 实现获取使用记录的API
          // const response = await getCouponUsageRecords(customerCouponId);

          // 暂时返回空数据，等待API实现
          return {
            data: [],
            success: true,
            total: 0,
          };
        } catch (error) {
          console.error('获取使用记录失败', error);
          message.error('获取使用记录失败');
          return {
            data: [],
            success: false,
            total: 0,
          };
        }
      }}
    />
  );
};

export default UsageRecordList;

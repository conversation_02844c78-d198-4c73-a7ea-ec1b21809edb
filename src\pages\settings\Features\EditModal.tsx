import { FeatureState } from '@/models/feature';
import {
  ModalForm,
  ProFormDigit,
  ProFormInstance,
  ProFormText,
  ProFormTextArea,
  ProFormTreeSelect,
} from '@ant-design/pro-components';
import { connect } from '@umijs/max';
import { Form, Input, InputNumber } from 'antd';
import React, { useCallback, useEffect, useRef, useState } from 'react';

type EditModalProps = {
  open: boolean;
  info?: API.Feature;
  onSave: (info: API.Feature) => Promise<void>;
  onClose: () => void;
  feature: FeatureState;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  onSave,
  onClose,
  feature,
}) => {
  const formRef = useRef<ProFormInstance>();
  const [parentCode, setParentCode] = useState<string>();

  /** 父节点变更后，实时计算 code */
  const calcCode = useCallback(() => {
    let code: string;
    let orderIndex: number;
    if (!parentCode) {
      console.log('parentCode is undefined');
      orderIndex = feature.list.filter((item) => !item.parentCode).length + 1;
      code = `${orderIndex}`.padStart(2, '0') + '0000';
    } else {
      console.log('parentCode is ', parentCode);
      orderIndex =
        feature.list.filter((item) => item.parentCode === parentCode).length +
        1;
      code =
        Number(parentCode) % 10000 === 0
          ? parentCode.substring(0, 2) + `${orderIndex}`.padStart(2, '0') + '00'
          : parentCode.substring(0, 4) + `${orderIndex}`.padStart(2, '0');
    }
    console.log('code is ', code);
    return {
      code: {
        first: Number(code.substring(0, 2)),
        second: Number(code.substring(2, 4)),
        third: Number(code.substring(4, 6)),
      },
      orderIndex,
    };
  }, [parentCode, feature]);

  useEffect(() => {
    const info = calcCode();
    formRef.current?.setFieldsValue(info);
  }, [parentCode]);

  useEffect(() => {
    if (info) {
      setParentCode(info.parentCode);
    } else {
      setParentCode(undefined);
    }
  }, [info]);

  return (
    <ModalForm<API.Feature>
      title={info ? '编辑功能' : '注册功能'}
      formRef={formRef}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
      }}
      open={open}
      layout="horizontal"
      grid
      labelCol={{ flex: '7em' }}
      onFinish={async (info) => {
        if (typeof info.code === 'object') {
          const { first, second, third } = info.code as any;
          info.code = `${first.toString().padStart(2, '0')}${second
            .toString()
            .padStart(2, '0')}${third.toString().padStart(2, '0')}`;
        }
        onSave(info);
      }}
      initialValues={
        info
          ? {
              ...info,
              code: {
                first: Number(info.code.substring(0, 2)),
                second: Number(info.code.substring(2, 4)),
                third: Number(info.code.substring(4, 6)),
              },
            }
          : calcCode()
      }
    >
      <ProFormTreeSelect
        name="parentCode"
        label="父级功能"
        // valueEnum={}
        params={{ treeData: feature.treeData }}
        request={async ({ treeData }) => {
          // 禁止选择自己，且不显示第三级节点
          (treeData as FeatureState['treeData']).forEach((item) => {
            if (item.key === info?.code) {
              item.disabled = true;
            }
            if (item.children) {
              if (item.key === info?.code) {
                item.disabled = true;
              }
              item.children.forEach((child: any) => {
                delete child.children;
              });
            }
          });
          return treeData;
        }}
        fieldProps={{
          onChange: (value) => {
            setParentCode(value);
          },
        }}
        readonly={!!info}
      />
      <ProFormText
        name="name"
        label="功能名称"
        rules={[{ required: true, message: '请输入功能名称！' }]}
        fieldProps={{ maxLength: 20 }}
        colProps={{ span: 12 }}
      />
      <ProFormText
        name="code"
        label="功能编号"
        rules={[{ required: true, message: '请输入功能编号！' }]}
        colProps={{ span: 12 }}
      >
        <Input.Group compact>
          <Form.Item name={['code', 'first']} noStyle>
            <InputNumber
              min={0}
              max={99}
              style={{ width: '25%' }}
              disabled={!!info || !!parentCode}
            />
          </Form.Item>
          <Form.Item name={['code', 'second']} noStyle>
            <InputNumber
              min={0}
              max={99}
              style={{ width: '25%' }}
              disabled={
                !!info ||
                (!!parentCode ? Number(parentCode) % 10000 !== 0 : true)
              }
            />
          </Form.Item>
          <Form.Item name={['code', 'third']} noStyle>
            <InputNumber
              min={0}
              max={99}
              style={{ width: '25%' }}
              disabled={
                !!info ||
                (!!parentCode ? Number(parentCode) % 10000 === 0 : true)
              }
            />
          </Form.Item>
        </Input.Group>
      </ProFormText>
      {/* <ProFormText name="icon" label="功能图标" />
      <ProFormText name="path" label="功能路径" /> */}
      <ProFormDigit
        name="orderIndex"
        label="排序"
        fieldProps={{ precision: 0 }}
        colProps={{ span: 6 }}
      />
      <ProFormTextArea
        name="description"
        label="描述"
        fieldProps={{
          maxLength: 100,
          showCount: true,
        }}
      />
    </ModalForm>
  );
};

export default connect(({ feature }) => ({ feature }))(EditModal);

import { overview, statusDistribution } from '@/services/order';
import { Pie } from '@ant-design/charts';
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { Card, Col, Row, Spin, Statistic, message } from 'antd';
import React, { useEffect, useState } from 'react';

interface OverviewData {
  orderStats: {
    total: number;
    today: number;
    month: number;
  };
  statusStats: Array<{
    status: string;
    count: number;
  }>;
  revenueStats: {
    total: number;
    today: number;
    month: number;
    completedOrders: number;
  };
}

interface StatusDistributionData {
  status: string;
  count: number;
  percentage: string;
  totalAmount: number;
}

const OverviewDashboard: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [overviewData, setOverviewData] = useState<OverviewData>();
  const [statusData, setStatusData] = useState<StatusDistributionData[]>([]);

  const fetchData = async () => {
    setLoading(true);
    try {
      const [overviewRes, statusRes] = await Promise.all([
        overview(),
        statusDistribution(),
      ]);

      if (overviewRes.errCode) {
        message.error(overviewRes.msg || '获取概览数据失败');
      } else {
        setOverviewData(overviewRes.data);
      }

      if (statusRes.errCode) {
        message.error(statusRes.msg || '获取状态分布数据失败');
      } else {
        setStatusData(statusRes.data || []);
      }
    } catch (error) {
      console.error('获取数据失败:', error);
      message.error('获取数据失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const pieData = statusData.map((item) => ({
    type: item.status,
    value: item.count,
  }));

  const pieConfig = {
    data: pieData,
    angleField: 'value',
    colorField: 'type',
    radius: 0.9,
    height: 300,
    label: {
      type: 'inner',
      offset: '-30%',
      content: '{value}',
      style: {
        fontSize: 14,
        textAlign: 'center',
      },
    },
    legend: {
      position: 'bottom' as const,
    },
    interactions: [
      {
        type: 'element-active',
      },
    ],
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总订单数"
              value={overviewData?.orderStats.total || 0}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="今日订单"
              value={overviewData?.orderStats.today || 0}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="本月订单"
              value={overviewData?.orderStats.month || 0}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总收入"
              value={overviewData?.revenueStats.total || 0}
              prefix={<DollarOutlined />}
              precision={2}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 今日收入统计 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="今日收入"
              value={overviewData?.revenueStats.today || 0}
              precision={2}
              suffix="元"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="本月收入"
              value={overviewData?.revenueStats.month || 0}
              precision={2}
              suffix="元"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="已完成订单"
              value={overviewData?.revenueStats.completedOrders || 0}
              suffix="单"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 订单状态分布图 */}
      <Row gutter={16}>
        <Col span={12}>
          <Card title="订单状态分布" style={{ height: '400px' }}>
            {statusData.length > 0 ? (
              <Pie {...pieConfig} />
            ) : (
              <div style={{ textAlign: 'center', padding: '50px' }}>
                暂无数据
              </div>
            )}
          </Card>
        </Col>
        <Col span={12}>
          <Card title="状态详情" style={{ height: '400px' }}>
            <div style={{ maxHeight: '320px', overflowY: 'auto' }}>
              {statusData.map((item, index) => (
                <div
                  key={index}
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    padding: '8px 0',
                    borderBottom: '1px solid #f0f0f0',
                  }}
                >
                  <span>{item.status}</span>
                  <span>
                    {item.count}单 ({item.percentage})
                  </span>
                </div>
              ))}
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default OverviewDashboard;

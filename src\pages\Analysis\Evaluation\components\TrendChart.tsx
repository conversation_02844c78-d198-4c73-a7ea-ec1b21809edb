import * as reviews from '@/services/reviews';
import { ProCard } from '@ant-design/pro-components';
import { Empty, message } from 'antd';
import { Dayjs } from 'dayjs';
import React, { useEffect, useState } from 'react';

interface TrendChartProps {
  dateRange: [Dayjs, Dayjs];
}

interface TrendData {
  date: string;
  reviews: number;
  averageRating: number;
}

const TrendChart: React.FC<TrendChartProps> = ({ dateRange }) => {
  const [trendData, setTrendData] = useState<TrendData[]>([]);
  const [loading, setLoading] = useState(false);

  // 获取趋势数据
  const fetchTrendData = async () => {
    setLoading(true);
    try {
      const { errCode, msg, data } = await reviews.trend({
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD'),
      });

      if (errCode) {
        message.error(msg || '获取趋势数据失败');
        return;
      }

      setTrendData(data || []);
    } catch (error) {
      console.error('获取趋势数据失败:', error);
      message.error('获取趋势数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTrendData();
  }, [dateRange]);

  // 简单的SVG图表实现
  const renderChart = () => {
    if (trendData.length === 0) {
      return <Empty description="暂无数据" />;
    }

    const width = 400;
    const height = 200;
    const padding = 40;
    const chartWidth = width - padding * 2;
    const chartHeight = height - padding * 2;

    const maxReviews = Math.max(...trendData.map((d) => d.reviews));
    const maxRating = 5;

    // 生成路径
    const reviewsPath = trendData
      .map((d, i) => {
        const x = padding + (i / (trendData.length - 1)) * chartWidth;
        const y =
          padding + chartHeight - (d.reviews / maxReviews) * chartHeight;
        return `${i === 0 ? 'M' : 'L'} ${x} ${y}`;
      })
      .join(' ');

    const ratingPath = trendData
      .map((d, i) => {
        const x = padding + (i / (trendData.length - 1)) * chartWidth;
        const y =
          padding + chartHeight - (d.averageRating / maxRating) * chartHeight;
        return `${i === 0 ? 'M' : 'L'} ${x} ${y}`;
      })
      .join(' ');

    return (
      <div style={{ textAlign: 'center' }}>
        <svg
          width={width}
          height={height}
          style={{ border: '1px solid #f0f0f0' }}
        >
          {/* 网格线 */}
          <defs>
            <pattern
              id="grid"
              width="40"
              height="40"
              patternUnits="userSpaceOnUse"
            >
              <path
                d="M 40 0 L 0 0 0 40"
                fill="none"
                stroke="#f0f0f0"
                strokeWidth="1"
              />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />

          {/* 评价数量线 */}
          <path d={reviewsPath} fill="none" stroke="#1890ff" strokeWidth="2" />

          {/* 平均评分线 */}
          <path d={ratingPath} fill="none" stroke="#52c41a" strokeWidth="2" />

          {/* 数据点 */}
          {trendData.map((d, i) => {
            const x = padding + (i / (trendData.length - 1)) * chartWidth;
            const reviewY =
              padding + chartHeight - (d.reviews / maxReviews) * chartHeight;
            const ratingY =
              padding +
              chartHeight -
              (d.averageRating / maxRating) * chartHeight;

            return (
              <g key={i}>
                <circle cx={x} cy={reviewY} r="3" fill="#1890ff" />
                <circle cx={x} cy={ratingY} r="3" fill="#52c41a" />
              </g>
            );
          })}

          {/* 图例 */}
          <g transform="translate(10, 10)">
            <circle cx="5" cy="5" r="3" fill="#1890ff" />
            <text x="15" y="9" fontSize="12" fill="#666">
              评价数量
            </text>
            <circle cx="80" cy="5" r="3" fill="#52c41a" />
            <text x="90" y="9" fontSize="12" fill="#666">
              平均评分
            </text>
          </g>
        </svg>

        <div style={{ marginTop: 8, fontSize: '12px', color: '#999' }}>
          评价趋势图 ({dateRange[0].format('MM-DD')} 至{' '}
          {dateRange[1].format('MM-DD')})
        </div>
      </div>
    );
  };

  return (
    <ProCard title="评价趋势分析" loading={loading} style={{ height: '300px' }}>
      {renderChart()}
    </ProCard>
  );
};

export default TrendChart;

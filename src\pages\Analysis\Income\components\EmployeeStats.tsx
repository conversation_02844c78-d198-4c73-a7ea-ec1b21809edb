import * as revenueStatistics from '@/services/revenue-statistics';
import { formatAmount } from '@/utils/format';
import { ProTable } from '@ant-design/pro-components';
import { Avatar, message } from 'antd';
import { Dayjs } from 'dayjs';
import React, { useRef } from 'react';

interface EmployeeStatsProps {
  dateRange: [Dayjs, Dayjs];
}

const EmployeeStats: React.FC<EmployeeStatsProps> = ({ dateRange }) => {
  const actionRef = useRef<any>();

  const columns = [
    {
      title: '员工信息',
      dataIndex: 'employee',
      key: 'employee',
      width: 200,
      render: (_: any, record: any) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <Avatar src={record.employee?.avatar} size="small">
            {record.employee?.name?.charAt(0)}
          </Avatar>
          <div>
            <div style={{ fontWeight: 500 }}>{record.employee?.name}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {record.employee?.phone}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: '总订单数',
      dataIndex: 'totalOrderCount',
      key: 'totalOrderCount',
      width: 100,
      render: (_: any, record: any) => {
        const value = record.totalOrderCount;
        return value !== null && value > 0 ? `${value}单` : '-';
      },
      sorter: true,
    },
    {
      title: '主订单数',
      dataIndex: ['mainOrder', 'orderCount'],
      key: 'mainOrderCount',
      width: 100,
      render: (_: any, record: any) => {
        const value = record.mainOrder?.orderCount;
        return value !== null && value > 0 ? `${value}单` : '-';
      },
    },
    {
      title: '追加服务订单数',
      dataIndex: ['additionalService', 'orderCount'],
      key: 'additionalOrderCount',
      width: 130,
      render: (_: any, record: any) => {
        const value = record.additionalService?.orderCount;
        return value !== null && value > 0 ? `${value}单` : '-';
      },
    },
    {
      title: '总原价',
      dataIndex: 'totalOriginalPrice',
      key: 'totalOriginalPrice',
      width: 120,
      render: (_: any, record: any) => {
        const value = record.totalOriginalPrice;
        return value !== null && value > 0 ? `¥${formatAmount(value)}` : '-';
      },
      sorter: true,
    },
    {
      title: '优惠金额',
      dataIndex: 'totalDiscount',
      key: 'totalDiscount',
      width: 120,
      render: (_: any, record: any) => {
        const value = record.totalDiscount;
        return value !== null && value > 0 ? `¥${formatAmount(value)}` : '-';
      },
      sorter: true,
    },
    {
      title: '优惠率',
      dataIndex: 'discountRate',
      key: 'discountRate',
      width: 100,
      render: (_: any, record: any) => {
        const value = record.discountRate;
        return value !== null ? `${value}%` : '-';
      },
      sorter: true,
    },
    {
      title: '实收金额',
      dataIndex: 'totalPaidAmount',
      key: 'totalPaidAmount',
      width: 120,
      render: (_: any, record: any) => {
        const value = record.totalPaidAmount;
        return value !== null && value > 0 ? `¥${formatAmount(value)}` : '-';
      },
      sorter: true,
    },
    {
      title: '退款金额',
      dataIndex: 'totalRefundedAmount',
      key: 'totalRefundedAmount',
      width: 120,
      render: (_: any, record: any) => {
        const value = record.totalRefundedAmount;
        return value !== null && value > 0 ? `¥${formatAmount(value)}` : '-';
      },
      sorter: true,
    },
    {
      title: '净收入',
      dataIndex: 'netRevenue',
      key: 'netRevenue',
      width: 120,
      render: (_: any, record: any) => {
        const value = record.netRevenue;
        return value !== null && value > 0 ? `¥${formatAmount(value)}` : '-';
      },
      sorter: true,
    },
    {
      title: '平均订单价值',
      dataIndex: 'avgOrderValue',
      key: 'avgOrderValue',
      width: 130,
      render: (_: any, record: any) => {
        const value = record.avgOrderValue;
        return value !== null && value !== '0.00'
          ? `¥${formatAmount(value)}`
          : '-';
      },
      sorter: true,
    },
  ];

  const fetchData = async (params: any, sort: any) => {
    try {
      const sortBy = sort && Object.keys(sort)[0];
      const sortOrder = sort && sort[sortBy] === 'ascend' ? 'asc' : 'desc';

      const { errCode, msg, data } = await revenueStatistics.employeeStats({
        startDate: dateRange[0]?.format('YYYY-MM-DD'),
        endDate: dateRange[1]?.format('YYYY-MM-DD'),
        page: params.current,
        pageSize: params.pageSize,
        sortBy: sortBy || 'netRevenue',
        sortOrder: sortOrder || 'desc',
      });

      if (errCode) {
        message.error(msg || '获取员工收入统计失败');
        return {
          data: [],
          success: false,
          total: 0,
        };
      }

      return {
        data: data?.list || [],
        success: true,
        total: data?.total || 0,
      };
    } catch (error) {
      console.error('获取员工收入统计失败:', error);
      message.error('获取员工收入统计失败');
      return {
        data: [],
        success: false,
        total: 0,
      };
    }
  };

  return (
    <ProTable
      actionRef={actionRef}
      columns={columns}
      request={fetchData}
      rowKey="employeeId"
      pagination={{
        defaultPageSize: 10,
        showSizeChanger: true,
        showQuickJumper: true,
      }}
      search={false}
      dateFormatter="string"
      headerTitle="员工收入统计"
      toolBarRender={false}
      size="small"
      scroll={{ x: 'max-content' }}
      params={{
        dateRange,
      }}
    />
  );
};

export default EmployeeStats;

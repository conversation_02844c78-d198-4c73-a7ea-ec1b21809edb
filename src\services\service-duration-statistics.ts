import { request } from '@umijs/max';

/**
 * 服务时长统计相关接口
 * 基础路径：/admin/service-duration-statistics
 */

/**
 * 按员工查询时长记录
 * GET /admin/service-duration-statistics/records
 */
export async function getEmployeeDurationRecords(params: {
  employeeId: number;
  startDate?: string;
  endDate?: string;
  page?: number;
  pageSize?: number;
}) {
  return request<API.ResType<API.EmployeeDurationRecordsResponse>>(
    '/admin/service-duration-statistics/records',
    {
      method: 'GET',
      params,
    },
  );
}

/**
 * 按订单查询时长统计详情
 * GET /admin/service-duration-statistics/order/{orderId}
 */
export async function getOrderDurationStatistics(orderId: number) {
  return request<API.ResType<API.OrderDurationStatistics>>(
    `/admin/service-duration-statistics/order/${orderId}`,
    {
      method: 'GET',
    },
  );
}

/**
 * 按主服务查询时长记录
 * GET /admin/service-duration-statistics/service/{serviceId}
 */
export async function getServiceDurationStatistics(
  serviceId: number,
  params?: {
    startDate?: string;
    endDate?: string;
    page?: number;
    pageSize?: number;
  },
) {
  return request<API.ResType<API.ServiceDurationStatisticsResponse>>(
    `/admin/service-duration-statistics/service/${serviceId}`,
    {
      method: 'GET',
      params,
    },
  );
}

/**
 * 按增项服务查询时长记录
 * GET /admin/service-duration-statistics/additional-service/{additionalServiceId}
 */
export async function getAdditionalServiceDurationStatistics(
  additionalServiceId: number,
  params?: {
    startDate?: string;
    endDate?: string;
    page?: number;
    pageSize?: number;
  },
) {
  return request<API.ResType<API.AdditionalServiceDurationStatisticsResponse>>(
    `/admin/service-duration-statistics/additional-service/${additionalServiceId}`,
    {
      method: 'GET',
      params,
    },
  );
}

/**
 * 格式化时长显示
 * @param duration 时长（分钟）
 * @returns 格式化后的时长字符串
 */
export function formatDuration(duration: number): string {
  if (duration < 60) {
    return `${duration}分钟`;
  }
  const hours = Math.floor(duration / 60);
  const minutes = duration % 60;
  if (minutes === 0) {
    return `${hours}小时`;
  }
  return `${hours}小时${minutes}分钟`;
}

/**
 * 计算时长差异百分比
 * @param actual 实际时长
 * @param expected 预期时长
 * @returns 差异百分比字符串
 */
export function calculateDurationDifference(
  actual: number,
  expected: number,
): string {
  if (expected === 0) return '0%';
  const difference = ((actual - expected) / expected) * 100;
  const sign = difference > 0 ? '+' : '';
  return `${sign}${difference.toFixed(1)}%`;
}

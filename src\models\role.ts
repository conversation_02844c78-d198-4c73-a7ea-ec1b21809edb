import { roles } from '@/services';
import {
  OnChangePayload,
  onProTableChange,
  onProTableSearch,
} from '@/utils/calc';
import { message } from 'antd';

export type RoleState = {
  total: number;
  list: API.Role[];
  currentList: API.Role[];
  inited: boolean;
};

export default {
  state: {
    total: 0,
    list: [],
    currentList: [],
    inited: false,
  } as RoleState,

  effects: {
    /** 查询列表 **/
    *queryList(
      { payload }: { payload: Record<string, any> },
      { call, put }: { call: Call; put: Put },
    ) {
      const { errCode, msg, data } = yield call(roles.index, payload);
      if (errCode) {
        message.error(msg || '角色列表查询失败');
      } else {
        yield put({ type: 'querySuccess', payload: data });
      }
    },
    *add(
      { payload }: { payload: API.Role },
      { call, put }: { call: Call; put: Put },
    ) {
      const { errCode, msg, data } = yield call(roles.create, payload);
      if (errCode) {
        message.error(msg || '角色创建失败');
      } else {
        message.success('角色创建成功');
        yield put({ type: 'addSuccess', payload: data });
      }
    },
    *update(
      { payload }: { payload: { id: number; info: Partial<API.Role> } },
      { call, put }: { call: Call; put: Put },
    ) {
      const { errCode, msg } = yield call(
        roles.update,
        payload.id,
        payload.info,
      );
      if (errCode) {
        message.error(msg || '角色更新失败');
      } else {
        message.success('角色更新成功');
        yield put({ type: 'updateSuccess', payload });
      }
    },
    *remove(
      { payload }: { payload: { id: number } },
      { call, put }: { call: Call; put: Put },
    ) {
      const { errCode, msg } = yield call(roles.remove, payload.id);
      if (errCode) {
        message.error(msg || '角色删除失败');
      } else {
        message.success('角色删除成功');
        yield put({ type: 'removeSuccess', payload });
      }
    },
  },

  reducers: {
    /** 查询列表成功后更新数据，触发渲染 */
    querySuccess(_state: RoleState, { payload }: { payload: RoleState }) {
      return { ...payload, currentList: payload.list, inited: true };
    },
    /** proTable组件的过滤和排序操作，分页不用处理，组件自己会完成 */
    onChange(
      state: RoleState,
      { payload }: { payload: OnChangePayload<API.Role> },
    ) {
      const filteredList = onProTableChange(state.list, payload);
      return {
        ...state,
        total: filteredList.length,
        currentList: filteredList,
      };
    },
    /** proTable组件的查询操作 */
    onSearch(state: RoleState, { payload }: { payload: Partial<API.Role> }) {
      const newList = onProTableSearch(state.list, payload);
      return {
        ...state,
        total: newList.length,
        currentList: newList,
      };
    },
    addSuccess(state: RoleState, { payload }: { payload: API.Role }) {
      const newList = [...state.list, payload];
      const currentList = [...state.currentList, payload];
      return {
        ...state,
        total: newList.length,
        list: newList,
        currentList,
      };
    },
    updateSuccess(
      state: RoleState,
      { payload }: { payload: { id: number; info: Partial<API.Role> } },
    ) {
      const newList = state.list.map((item) => {
        if (item.id === payload.id) {
          return { ...item, ...payload.info };
        }
        return item;
      });
      const currentList = state.currentList.map((item) => {
        if (item.id === payload.id) {
          return { ...item, ...payload.info };
        }
        return item;
      });
      return {
        ...state,
        list: newList,
        currentList,
      };
    },
    removeSuccess(state: RoleState, { payload }: { payload: { id: number } }) {
      const newList = state.list.filter((item) => item.id !== payload.id);
      const currentList = state.currentList.filter(
        (item) => item.id !== payload.id,
      );
      return {
        ...state,
        list: newList,
        currentList,
      };
    },
  },
};

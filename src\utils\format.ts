/**
 * 格式化工具函数
 */

/**
 * 安全地格式化金额
 * @param amount 金额值
 * @param decimals 小数位数，默认2位
 * @returns 格式化后的金额字符串
 */
export function formatAmount(amount: any, decimals: number = 2): string {
  if (typeof amount === 'number' && !isNaN(amount)) {
    return amount.toFixed(decimals);
  }

  // 尝试转换为数字
  const numAmount = Number(amount);
  if (!isNaN(numAmount)) {
    return numAmount.toFixed(decimals);
  }

  // 如果无法转换，返回默认值
  return '0.00';
}

/**
 * 格式化金额并添加货币符号
 * @param amount 金额值
 * @param currency 货币符号，默认为 ¥
 * @param decimals 小数位数，默认2位
 * @returns 格式化后的金额字符串（带货币符号）
 */
export function formatCurrency(
  amount: any,
  currency: string = '¥',
  decimals: number = 2,
): string {
  return `${currency}${formatAmount(amount, decimals)}`;
}

/**
 * 安全地格式化百分比
 * @param value 数值
 * @param decimals 小数位数，默认1位
 * @returns 格式化后的百分比字符串
 */
export function formatPercentage(value: any, decimals: number = 1): string {
  if (typeof value === 'number' && !isNaN(value)) {
    return `${(value * 100).toFixed(decimals)}%`;
  }

  const numValue = Number(value);
  if (!isNaN(numValue)) {
    return `${(numValue * 100).toFixed(decimals)}%`;
  }

  return '0.0%';
}

/**
 * 安全地格式化数字
 * @param value 数值
 * @param decimals 小数位数，默认0位
 * @returns 格式化后的数字字符串
 */
export function formatNumber(value: any, decimals: number = 0): string {
  if (typeof value === 'number' && !isNaN(value)) {
    return value.toFixed(decimals);
  }

  const numValue = Number(value);
  if (!isNaN(numValue)) {
    return numValue.toFixed(decimals);
  }

  return '0';
}

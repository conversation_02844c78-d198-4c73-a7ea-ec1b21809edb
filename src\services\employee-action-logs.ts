/*
 * @Description: 员工动作记录管理
 */
import { request } from '@umijs/max';

/** 获取员工动作记录列表 GET /employee-action-logs/list */
export async function index(params: {
  current?: number;
  pageSize?: number;
  startDate?: string;
  endDate?: string;
  employeeId?: number;
  orderId?: number;
  changeType?: string;
}) {
  return request<
    API.ResType<{ total?: number; list?: API.EmployeeActionLog[] }>
  >('/employee-action-logs/list', {
    method: 'GET',
    params: {
      ...params,
      page: params.current,
    },
  });
}

/** 获取员工动作统计概览 GET /employee-action-logs/overview */
export async function overview(params: {
  startDate?: string;
  endDate?: string;
  employeeId?: number;
}) {
  return request<API.ResType<API.EmployeeActionOverview>>(
    '/employee-action-logs/overview',
    {
      method: 'GET',
      params,
    },
  );
}

/** 按员工统计动作记录 GET /employee-action-logs/stats-by-employee */
export async function statsByEmployee(params: {
  current?: number;
  pageSize?: number;
  startDate?: string;
  endDate?: string;
}) {
  return request<
    API.ResType<{ total?: number; list?: API.EmployeeActionStatsByEmployee[] }>
  >('/employee-action-logs/stats-by-employee', {
    method: 'GET',
    params: {
      ...params,
      page: params.current,
    },
  });
}

/** 获取员工动作时间线 GET /employee-action-logs/timeline */
export async function timeline(params: {
  employeeId: number;
  startDate?: string;
  endDate?: string;
  orderId?: number;
}) {
  return request<API.ResType<API.EmployeeActionTimeline[]>>(
    '/employee-action-logs/timeline',
    {
      method: 'GET',
      params,
    },
  );
}

import * as order from '@/services/order';
import { ProCard } from '@ant-design/pro-components';
import { Empty, List, message, Tag } from 'antd';
import { Dayjs } from 'dayjs';
import React, { useEffect, useState } from 'react';

interface RegionStatsProps {
  dateRange: [Dayjs, Dayjs];
}

interface RegionData {
  region: string;
  orderCount: number;
  totalAmount: number;
  avgAmount: number;
}

const RegionStats: React.FC<RegionStatsProps> = ({ dateRange }) => {
  const [regionData, setRegionData] = useState<RegionData[]>([]);
  const [loading, setLoading] = useState(false);

  // 获取地区统计数据
  const fetchRegionData = async () => {
    setLoading(true);
    try {
      const { errCode, msg, data } = await order.regionStats({
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD'),
      });

      if (errCode) {
        message.error(msg || '获取地区统计失败');
        return;
      }

      setRegionData(data || []);
    } catch (error) {
      console.error('获取地区统计失败:', error);
      message.error('获取地区统计失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRegionData();
  }, [dateRange]);

  // 渲染地区统计
  const renderRegionStats = () => {
    if (regionData.length === 0) {
      return <Empty description="暂无数据" />;
    }

    // 按订单数量排序
    const sortedData = [...regionData].sort(
      (a, b) => b.orderCount - a.orderCount,
    );

    return (
      <List
        dataSource={sortedData}
        renderItem={(item, index) => {
          // 根据排名设置不同的标签颜色
          let tagColor = 'default';
          if (index === 0) tagColor = 'red';
          else if (index === 1) tagColor = 'orange';
          else if (index === 2) tagColor = 'gold';
          else if (index < 5) tagColor = 'blue';

          return (
            <List.Item style={{ padding: '12px 0' }}>
              <div style={{ width: '100%' }}>
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginBottom: 8,
                  }}
                >
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <Tag color={tagColor} style={{ margin: 0, marginRight: 8 }}>
                      #{index + 1}
                    </Tag>
                    <span style={{ fontWeight: 500, fontSize: '14px' }}>
                      {item.region}
                    </span>
                  </div>
                  <span style={{ fontSize: '12px', color: '#999' }}>
                    {item.orderCount} 单
                  </span>
                </div>

                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    fontSize: '12px',
                    color: '#666',
                  }}
                >
                  <span>总金额: ¥{item.totalAmount.toFixed(2)}</span>
                  <span>平均: ¥{item.avgAmount.toFixed(2)}</span>
                </div>
              </div>
            </List.Item>
          );
        }}
      />
    );
  };

  return (
    <ProCard
      title="地区订单分布"
      loading={loading}
      style={{ height: '400px', overflow: 'auto' }}
      extra={
        <span style={{ fontSize: '12px', color: '#999' }}>按订单数量排序</span>
      }
    >
      {renderRegionStats()}
    </ProCard>
  );
};

export default RegionStats;

import { request } from '@umijs/max';

/** 查询服务照片列表  GET /service-photos */
export async function index(params: Record<string, any>) {
  return request<API.ResType<{ total?: number; list?: API.ServicePhoto[] }>>(
    '/service-photos',
    {
      method: 'GET',
      params,
    },
  );
}

/** 按ID查询服务照片  GET /service-photos/:id */
export async function show(id: number) {
  return request<API.ResType<API.ServicePhoto>>(`/service-photos/${id}`, {
    method: 'GET',
  });
}

/** 查询员工的服务照片列表  GET /service-photos/employee/:employeeId */
export async function getByEmployee(
  employeeId: number,
  params?: Record<string, any>,
) {
  return request<API.ResType<{ total?: number; list?: API.ServicePhoto[] }>>(
    `/service-photos/employee/${employeeId}`,
    {
      method: 'GET',
      params,
    },
  );
}

import { index } from '@/services/service-photos';
import { EyeOutlined, PictureOutlined } from '@ant-design/icons';
import {
  Card,
  Col,
  Empty,
  Image,
  message,
  Modal,
  Row,
  Spin,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';

const { Title, Text } = Typography;

interface ServicePhotosModalProps {
  visible: boolean;
  orderId?: number;
  onClose: () => void;
}

const ServicePhotosModal: React.FC<ServicePhotosModalProps> = ({
  visible,
  orderId,
  onClose,
}) => {
  const [loading, setLoading] = useState(false);
  const [photos, setPhotos] = useState<API.ServicePhoto[]>([]);

  const fetchPhotos = async () => {
    if (!orderId) return;

    setLoading(true);
    try {
      const { errCode, msg, data } = await index({ orderId });
      if (errCode) {
        message.error(msg || '获取服务照片失败');
      } else {
        setPhotos(data?.list || []);
      }
    } catch (error) {
      console.error('获取服务照片失败:', error);
      message.error('获取服务照片失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible && orderId) {
      fetchPhotos();
    }
  }, [visible, orderId]);

  const renderPhotoSection = (
    title: string,
    photos: string[],
    time?: string,
    icon?: React.ReactNode,
  ) => (
    <Card
      size="small"
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          {icon}
          <span>{title}</span>
          {time && (
            <Text
              type="secondary"
              style={{ fontSize: '12px', fontWeight: 'normal' }}
            >
              {dayjs(time).format('YYYY-MM-DD HH:mm:ss')}
            </Text>
          )}
        </div>
      }
      style={{ marginBottom: '16px' }}
    >
      {photos && photos.length > 0 ? (
        <Row gutter={[8, 8]}>
          {photos.map((photo, index) => (
            <Col key={index} span={6}>
              <Image
                src={photo}
                alt={`${title}${index + 1}`}
                style={{
                  width: '100%',
                  height: '120px',
                  objectFit: 'cover',
                  borderRadius: '4px',
                }}
                preview={{
                  mask: (
                    <div style={{ textAlign: 'center' }}>
                      <EyeOutlined style={{ fontSize: '20px' }} />
                      <div style={{ marginTop: '4px', fontSize: '12px' }}>
                        预览
                      </div>
                    </div>
                  ),
                }}
              />
            </Col>
          ))}
        </Row>
      ) : (
        <Empty
          image={
            <PictureOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
          }
          description={`暂无${title}`}
          style={{ margin: '20px 0' }}
        />
      )}
    </Card>
  );

  return (
    <Modal
      title="服务照片"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      destroyOnClose
    >
      <Spin spinning={loading}>
        {photos.length > 0 ? (
          <div>
            {photos.map((photoRecord) => (
              <div key={photoRecord.id} style={{ marginBottom: '24px' }}>
                {photoRecord.employee && (
                  <Title level={5} style={{ marginBottom: '16px' }}>
                    服务人员：{photoRecord.employee.name}
                  </Title>
                )}

                {renderPhotoSection(
                  '服务前照片',
                  photoRecord.beforePhotos || [],
                  photoRecord.beforePhotoTime,
                  <PictureOutlined style={{ color: '#1890ff' }} />,
                )}

                {renderPhotoSection(
                  '服务后照片',
                  photoRecord.afterPhotos || [],
                  photoRecord.afterPhotoTime,
                  <PictureOutlined style={{ color: '#52c41a' }} />,
                )}
              </div>
            ))}
          </div>
        ) : (
          !loading && (
            <Empty
              image={
                <PictureOutlined
                  style={{ fontSize: '64px', color: '#d9d9d9' }}
                />
              }
              description="该订单暂无服务照片"
              style={{ margin: '40px 0' }}
            />
          )
        )}
      </Spin>
    </Modal>
  );
};

export default ServicePhotosModal;

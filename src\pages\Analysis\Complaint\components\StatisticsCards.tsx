import {
  AlertOutlined,
  BulbOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  FileTextOutlined,
  RiseOutlined,
} from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { Card, Col, Row, Statistic } from 'antd';
import React from 'react';

interface StatisticsCardsProps {
  data: API.ComplaintOverviewStats;
  loading: boolean;
}

const StatisticsCards: React.FC<StatisticsCardsProps> = ({ data, loading }) => {
  return (
    <ProCard
      title="投诉建议数据概览"
      style={{ marginBottom: 16 }}
      loading={loading}
    >
      <Row gutter={[16, 16]}>
        <Col xs={12} sm={8} md={6} lg={4} xl={4}>
          <Card size="small" style={{ height: '100%', textAlign: 'center' }}>
            <Statistic
              title="总投诉建议数"
              value={data.complaintStats.total}
              prefix={<FileTextOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff', fontSize: '18px' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6} lg={4} xl={4}>
          <Card size="small" style={{ height: '100%', textAlign: 'center' }}>
            <Statistic
              title="今日新增"
              value={data.complaintStats.today}
              prefix={<CalendarOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a', fontSize: '18px' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6} lg={4} xl={4}>
          <Card size="small" style={{ height: '100%', textAlign: 'center' }}>
            <Statistic
              title="本周新增"
              value={data.complaintStats.week}
              prefix={<RiseOutlined style={{ color: '#722ed1' }} />}
              valueStyle={{ color: '#722ed1', fontSize: '18px' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6} lg={4} xl={4}>
          <Card size="small" style={{ height: '100%', textAlign: 'center' }}>
            <Statistic
              title="本月新增"
              value={data.complaintStats.month}
              prefix={<ClockCircleOutlined style={{ color: '#fa8c16' }} />}
              valueStyle={{ color: '#fa8c16', fontSize: '18px' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6} lg={4} xl={4}>
          <Card size="small" style={{ height: '100%', textAlign: 'center' }}>
            <Statistic
              title="已处理数量"
              value={data.complaintStats.processedCount}
              prefix={<CheckCircleOutlined style={{ color: '#13c2c2' }} />}
              valueStyle={{ color: '#13c2c2', fontSize: '18px' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6} lg={4} xl={4}>
          <Card size="small" style={{ height: '100%', textAlign: 'center' }}>
            <Statistic
              title="处理率"
              value={data.complaintStats.processRate}
              precision={1}
              suffix="%"
              prefix={<AlertOutlined style={{ color: '#eb2f96' }} />}
              valueStyle={{ color: '#eb2f96', fontSize: '18px' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 分类统计 */}
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        {data.categoryStats.map((item) => (
          <Col xs={12} sm={8} md={6} lg={4} xl={4} key={item.category}>
            <Card size="small" style={{ height: '100%', textAlign: 'center' }}>
              <Statistic
                title={item.category === 'complaint' ? '投诉数量' : '建议数量'}
                value={item.count}
                prefix={
                  item.category === 'complaint' ? (
                    <AlertOutlined style={{ color: '#ff4d4f' }} />
                  ) : (
                    <BulbOutlined style={{ color: '#1890ff' }} />
                  )
                }
                valueStyle={{
                  color: item.category === 'complaint' ? '#ff4d4f' : '#1890ff',
                  fontSize: '18px',
                }}
              />
            </Card>
          </Col>
        ))}
      </Row>
    </ProCard>
  );
};

export default StatisticsCards;

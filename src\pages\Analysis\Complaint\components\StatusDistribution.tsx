import { Pie<PERSON><PERSON>, PieChartData } from '@/components/Charts';
import * as complaints from '@/services/complaints';
import { ProCard } from '@ant-design/pro-components';
import { message, Select, Space } from 'antd';
import { Dayjs } from 'dayjs';
import React, { useEffect, useState } from 'react';

interface StatusDistributionProps {
  dateRange: [Dayjs, Dayjs];
  viewType?: 'status' | 'category' | 'subCategory';
  title?: string;
  showSelector?: boolean;
}

const StatusDistribution: React.FC<StatusDistributionProps> = ({
  dateRange,
  viewType: propViewType = 'status',
  title: propTitle,
  showSelector = true,
}) => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<API.ComplaintStatusDistributionStats>({
    statusStats: [],
    categoryStats: [],
    subCategoryStats: [],
  });
  const [viewType, setViewType] = useState<
    'status' | 'category' | 'subCategory'
  >(propViewType);

  const fetchData = async () => {
    setLoading(true);
    try {
      const {
        errCode,
        msg,
        data: responseData,
      } = await complaints.statusDistribution({
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD'),
      });

      if (errCode) {
        message.error(msg || '获取状态分布数据失败');
        // 使用模拟数据进行测试
        const mockData: API.ComplaintStatusDistributionStats = {
          statusStats: [
            { status: 'pending', count: 10, percentage: 40 },
            { status: 'processing', count: 8, percentage: 32 },
            { status: 'resolved', count: 5, percentage: 20 },
            { status: 'closed', count: 2, percentage: 8 },
          ],
          categoryStats: [
            { category: 'complaint', count: 15, percentage: 60 },
            { category: 'suggestion', count: 10, percentage: 40 },
          ],
          subCategoryStats: [
            { subCategory: 'order', count: 8, percentage: 32 },
            { subCategory: 'employee', count: 5, percentage: 20 },
            { subCategory: 'platform', count: 7, percentage: 28 },
            { subCategory: 'service', count: 3, percentage: 12 },
            { subCategory: 'workflow', count: 2, percentage: 8 },
          ],
        };
        setData(mockData);
        return;
      }

      if (responseData) {
        setData(responseData);
      } else {
        // 如果没有数据，也使用模拟数据
        const mockData: API.ComplaintStatusDistributionStats = {
          statusStats: [
            { status: 'pending', count: 5, percentage: 50 },
            { status: 'resolved', count: 5, percentage: 50 },
          ],
          categoryStats: [
            { category: 'complaint', count: 6, percentage: 60 },
            { category: 'suggestion', count: 4, percentage: 40 },
          ],
          subCategoryStats: [
            { subCategory: 'order', count: 4, percentage: 40 },
            { subCategory: 'service', count: 3, percentage: 30 },
            { subCategory: 'platform', count: 3, percentage: 30 },
          ],
        };
        setData(mockData);
      }
    } catch (error) {
      console.error('获取状态分布数据失败:', error);
      message.error('获取状态分布数据失败');
      // 异常情况下也使用模拟数据
      const mockData: API.ComplaintStatusDistributionStats = {
        statusStats: [
          { status: 'pending', count: 3, percentage: 60 },
          { status: 'resolved', count: 2, percentage: 40 },
        ],
        categoryStats: [
          { category: 'complaint', count: 3, percentage: 60 },
          { category: 'suggestion', count: 2, percentage: 40 },
        ],
        subCategoryStats: [
          { subCategory: 'order', count: 2, percentage: 40 },
          { subCategory: 'service', count: 2, percentage: 40 },
          { subCategory: 'platform', count: 1, percentage: 20 },
        ],
      };
      setData(mockData);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [dateRange]);

  // 获取当前视图的数据
  const getCurrentData = (): PieChartData[] => {
    const statusLabels = {
      pending: '待处理',
      processing: '处理中',
      resolved: '已解决',
      closed: '已关闭',
    };

    const categoryLabels = {
      complaint: '投诉',
      suggestion: '建议',
    };

    const subCategoryLabels = {
      order: '订单投诉',
      employee: '人员投诉',
      platform: '平台建议',
      service: '服务建议',
      workflow: '流程投诉',
    };

    switch (viewType) {
      case 'status':
        return data.statusStats.map((item) => ({
          type:
            statusLabels[item.status as keyof typeof statusLabels] ||
            item.status,
          value: item.count,
          percentage: item.percentage,
        }));
      case 'category':
        return data.categoryStats.map((item) => ({
          type:
            categoryLabels[item.category as keyof typeof categoryLabels] ||
            item.category,
          value: item.count,
          percentage: item.percentage,
        }));
      case 'subCategory':
        return data.subCategoryStats.map((item) => ({
          type:
            subCategoryLabels[
              item.subCategory as keyof typeof subCategoryLabels
            ] || item.subCategory,
          value: item.count,
          percentage: item.percentage,
        }));
      default:
        return [];
    }
  };

  // 获取标题
  const getTitle = () => {
    if (propTitle) return propTitle;

    const titleMap = {
      status: '状态分布',
      category: '类型分布',
      subCategory: '子类分布',
    };
    return titleMap[viewType];
  };

  const chartData = getCurrentData();

  return (
    <ProCard
      title={getTitle()}
      loading={loading}
      extra={
        showSelector ? (
          <Space>
            <Select
              value={viewType}
              onChange={setViewType}
              style={{ width: 120 }}
              options={[
                { label: '状态分布', value: 'status' },
                { label: '类型分布', value: 'category' },
                { label: '子类分布', value: 'subCategory' },
              ]}
            />
          </Space>
        ) : null
      }
    >
      <PieChart
        data={chartData}
        height={280}
        colors={[
          '#1890ff',
          '#52c41a',
          '#faad14',
          '#f5222d',
          '#722ed1',
          '#13c2c2',
        ]}
        labelPosition="spider"
        valueUnit="件"
        emptyText="暂无数据"
      />
    </ProCard>
  );
};

export default StatusDistribution;

import { request } from '@umijs/max';

/** 查询列表  GET /service-type */
export async function index(params: Record<string, any>) {
  return request<API.ResType<{ total?: number; list?: API.ServiceType[] }>>(
    '/service-type',
    {
      method: 'GET',
      params,
    },
  );
}

/** 创建  POST /service-type */
export async function create(body: Omit<API.ServiceType, 'id'>) {
  return request<API.ResType<API.ServiceType>>('/service-type', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 按ID查询  GET /service-type/:id */
export async function show(id: number) {
  return request<API.ResType<API.ServiceType>>(`/service-type/${id}`, {
    method: 'GET',
  });
}

/** 修改  PUT /service-type/:id */
export async function update(id: number, body: Partial<API.ServiceType>) {
  return request<API.ResType<unknown>>(`/service-type/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 删除  DELETE /service-type/:id */
export async function remove(id: number) {
  return request<API.ResType<unknown>>(`/service-type/${id}`, {
    method: 'DELETE',
  });
}

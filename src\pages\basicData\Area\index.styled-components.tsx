import { styled } from '@umijs/max';
import { Tree, TreeProps } from 'antd';

interface Props extends TreeProps {
  mycolor?: string;
}

/** 树型组件操作区与hove互动 */
export const OptionsTreeStyled = styled(Tree)<Props>`
  .ant-tree-treenode {
    .ant-tree-node-content-wrapper {
      .ant-tree-title {
        .options {
          display: none;
        }
      }
      &.ant-tree-node-selected,
      &:hover {
        .ant-tree-title {
          .options {
            display: flex;
            flex-direction: column;
            justify-content: space-around;
          }
        }
      }
    }
  }
`;

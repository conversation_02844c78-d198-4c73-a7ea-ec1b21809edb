import React from 'react';
import styles from './index.less';

/**
 * 标记点渲染组件
 * 用于自定义标记点的渲染
 */
export const MarkerContent: React.FC<{
  marker: Marker;
  isActive?: boolean;
}> = ({ marker, isActive = false }) => {
  // 如果标记点有自定义内容，直接返回
  if (marker.content) {
    return <div dangerouslySetInnerHTML={{ __html: marker.content }} />;
  }

  // 默认标记点样式
  const style: React.CSSProperties = {
    backgroundColor: isActive ? '#e74c3c' : '#3498db',
    width: '12px',
    height: '12px',
    border: '2px solid white',
    borderRadius: '50%',
    boxShadow: '0 0 5px rgba(0,0,0,0.3)',
  };

  return <div style={style} />;
};

/**
 * 信息窗体内容组件
 * 用于渲染标记点的信息窗体内容
 */
export const InfoWindowContent: React.FC<{
  content: string;
}> = ({ content }) => {
  return <div dangerouslySetInnerHTML={{ __html: content }} />;
};

/**
 * 自定义控件容器组件
 * 用于渲染地图上的自定义控件
 */
export const CustomControlContainer: React.FC<{
  position: 'LT' | 'RT' | 'LB' | 'RB';
  children: React.ReactNode;
  className?: string;
}> = ({ position, children, className }) => {
  // 根据位置设置样式
  const getPositionStyle = (): React.CSSProperties => {
    switch (position) {
      case 'LT':
        return { top: '10px', left: '10px' };
      case 'RT':
        return { top: '10px', right: '10px' };
      case 'LB':
        return { bottom: '10px', left: '10px' };
      case 'RB':
        return { bottom: '10px', right: '10px' };
      default:
        return { top: '10px', right: '10px' };
    }
  };

  const style: React.CSSProperties = {
    position: 'absolute',
    zIndex: 100,
    backgroundColor: 'white',
    borderRadius: '2px',
    boxShadow: '0 2px 6px rgba(0, 0, 0, 0.3)',
    overflow: 'hidden',
    margin: '10px',
    padding: '5px',
    ...getPositionStyle(),
  };

  return (
    <div className={`${styles.customControl} ${className || ''}`} style={style}>
      {children}
    </div>
  );
};

/**
 * 地图加载状态组件
 */
export const MapLoadingStatus: React.FC<{
  loading: boolean;
  error: string;
  loadingText: string;
}> = ({ loading, error, loadingText }) => {
  if (loading) {
    return (
      <div className={styles.mapLoading}>
        <div className={styles.loadingText}>{loadingText}</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.mapError}>
        <div className={styles.errorText}>{error}</div>
      </div>
    );
  }

  return null;
};

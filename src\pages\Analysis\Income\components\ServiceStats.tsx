import * as revenueStatistics from '@/services/revenue-statistics';
import { formatAmount } from '@/utils/format';
import { ProTable } from '@ant-design/pro-components';
import { message } from 'antd';
import { Dayjs } from 'dayjs';
import React, { useRef } from 'react';

interface ServiceStatsProps {
  dateRange: [Dayjs, Dayjs];
}

const ServiceStats: React.FC<ServiceStatsProps> = ({ dateRange }) => {
  const actionRef = useRef<any>();

  const columns = [
    {
      title: '服务名称',
      dataIndex: 'serviceName',
      key: 'serviceName',
      width: 150,
      ellipsis: true,
    },
    {
      title: '服务类型',
      dataIndex: 'serviceType',
      key: 'serviceType',
      width: 120,
    },
    {
      title: '总订单数',
      dataIndex: 'totalOrderCount',
      key: 'totalOrderCount',
      width: 100,
      render: (_: any, record: API.RevenueServiceStats) =>
        record.totalOrderCount !== null ? `${record.totalOrderCount}单` : '-',
      sorter: true,
    },
    {
      title: '退款订单数',
      dataIndex: 'refundedOrderCount',
      key: 'refundedOrderCount',
      width: 100,
      render: (_: any, record: API.RevenueServiceStats) =>
        record.refundedOrderCount !== null
          ? `${record.refundedOrderCount}单`
          : '-',
      sorter: true,
    },
    {
      title: '总原价',
      dataIndex: 'totalOriginalPrice',
      key: 'totalOriginalPrice',
      width: 120,
      render: (_: any, record: API.RevenueServiceStats) =>
        record.totalOriginalPrice !== null
          ? `¥${formatAmount(record.totalOriginalPrice)}`
          : '-',
      sorter: true,
    },
    {
      title: '优惠金额',
      dataIndex: 'totalDiscount',
      key: 'totalDiscount',
      width: 120,
      render: (_: any, record: API.RevenueServiceStats) =>
        record.totalDiscount !== null
          ? `¥${formatAmount(record.totalDiscount)}`
          : '-',
      sorter: true,
    },
    {
      title: '优惠率',
      dataIndex: 'discountRate',
      key: 'discountRate',
      width: 100,
      render: (_: any, record: API.RevenueServiceStats) =>
        record.discountRate !== null ? `${record.discountRate}%` : '-',
      sorter: true,
    },
    {
      title: '实收金额',
      dataIndex: 'totalPaidAmount',
      key: 'totalPaidAmount',
      width: 120,
      render: (_: any, record: API.RevenueServiceStats) =>
        record.totalPaidAmount !== null
          ? `¥${formatAmount(record.totalPaidAmount)}`
          : '-',
      sorter: true,
    },
    {
      title: '退款金额',
      dataIndex: 'totalRefundedAmount',
      key: 'totalRefundedAmount',
      width: 120,
      render: (_: any, record: API.RevenueServiceStats) =>
        record.totalRefundedAmount !== null
          ? `¥${formatAmount(record.totalRefundedAmount)}`
          : '-',
      sorter: true,
    },
    {
      title: '净收入',
      dataIndex: 'netRevenue',
      key: 'netRevenue',
      width: 120,
      render: (_: any, record: API.RevenueServiceStats) =>
        record.netRevenue !== null
          ? `¥${formatAmount(record.netRevenue)}`
          : '-',
      sorter: true,
    },
    {
      title: '平均订单价值',
      dataIndex: 'avgOrderValue',
      key: 'avgOrderValue',
      width: 130,
      render: (_: any, record: API.RevenueServiceStats) =>
        record.avgOrderValue !== null
          ? `¥${formatAmount(record.avgOrderValue)}`
          : '-',
      sorter: true,
    },
  ];

  const fetchData = async (params: any, sort: any) => {
    try {
      const sortBy = sort && Object.keys(sort)[0];
      const sortOrder = sort && sort[sortBy] === 'ascend' ? 'asc' : 'desc';

      const { errCode, msg, data } = await revenueStatistics.serviceStats({
        startDate: dateRange[0]?.format('YYYY-MM-DD'),
        endDate: dateRange[1]?.format('YYYY-MM-DD'),
        page: params.current,
        pageSize: params.pageSize,
        sortBy: sortBy || 'netRevenue',
        sortOrder: sortOrder || 'desc',
      });

      if (errCode) {
        message.error(msg || '获取服务收入统计失败');
        return {
          data: [],
          success: false,
          total: 0,
        };
      }

      return {
        data: data?.list || [],
        success: true,
        total: data?.total || 0,
      };
    } catch (error) {
      console.error('获取服务收入统计失败:', error);
      message.error('获取服务收入统计失败');
      return {
        data: [],
        success: false,
        total: 0,
      };
    }
  };

  return (
    <ProTable
      actionRef={actionRef}
      columns={columns}
      request={fetchData}
      rowKey="serviceId"
      pagination={{
        defaultPageSize: 10,
        showSizeChanger: true,
        showQuickJumper: true,
      }}
      search={false}
      dateFormatter="string"
      headerTitle="服务收入统计"
      toolBarRender={false}
      size="small"
      scroll={{ x: 'max-content' }}
      params={{
        dateRange,
      }}
    />
  );
};

export default ServiceStats;

import { request } from '@umijs/max';

/**
 * 用户数据统计相关接口
 * 基础路径：/customer-statistics
 */

/** 用户概览统计  GET /customer-statistics/overview */
export async function overview(params?: {
  startDate?: string;
  endDate?: string;
}) {
  return request<API.ResType<API.CustomerOverviewStats>>(
    '/customer-statistics/overview',
    {
      method: 'GET',
      params,
    },
  );
}

/** 用户注册趋势统计  GET /customer-statistics/registration-trend */
export async function registrationTrend(params: {
  startDate: string;
  endDate: string;
  periodType?: 'day' | 'week' | 'month';
}) {
  return request<API.ResType<API.CustomerRegistrationTrend[]>>(
    '/customer-statistics/registration-trend',
    {
      method: 'GET',
      params,
    },
  );
}

/** 用户性别分布统计  GET /customer-statistics/gender-distribution */
export async function genderDistribution(params?: {
  startDate?: string;
  endDate?: string;
}) {
  return request<API.ResType<API.CustomerGenderDistribution[]>>(
    '/customer-statistics/gender-distribution',
    {
      method: 'GET',
      params,
    },
  );
}

/** 用户会员状态分布统计  GET /customer-statistics/member-status-distribution */
export async function memberStatusDistribution(params?: {
  startDate?: string;
  endDate?: string;
}) {
  return request<API.ResType<API.CustomerMemberStatusDistribution[]>>(
    '/customer-statistics/member-status-distribution',
    {
      method: 'GET',
      params,
    },
  );
}

/** 用户统计汇总  GET /customer-statistics/summary */
export async function summary(params?: {
  startDate?: string;
  endDate?: string;
}) {
  return request<API.ResType<API.CustomerStatisticsSummary>>(
    '/customer-statistics/summary',
    {
      method: 'GET',
      params,
    },
  );
}

/** 用户增长趋势（最近30天）  GET /customer-statistics/growth-trend */
export async function growthTrend() {
  return request<API.ResType<API.CustomerGrowthTrend>>(
    '/customer-statistics/growth-trend',
    {
      method: 'GET',
    },
  );
}

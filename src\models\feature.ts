import { features } from '@/services';
import { convertListToTree } from '@/utils/calc';
import { message } from 'antd';

interface TreeNode extends API.Feature {
  label: string;
  value: string;
  title: string;
  key: string;
  children?: TreeNode[];
  [key: string]: any;
}

export type FeatureState = {
  list: API.Feature[];
  treeData: TreeNode[];
  inited: boolean;
};
export default {
  state: {
    list: [],
    treeData: [],
    inited: false,
  } as FeatureState,

  effects: {
    /** 查询列表 **/
    *queryList(
      { payload }: { payload: Record<string, any> },
      { call, put }: { call: Call; put: Put },
    ) {
      const { errCode, msg, data } = yield call(features.index, payload);
      if (errCode) {
        message.error(msg || '功能列表查询失败');
      } else {
        yield put({ type: 'querySuccess', payload: data.list || [] });
      }
    },
    *add(
      { payload }: { payload: API.Feature },
      { call, put }: { call: Call; put: Put },
    ) {
      const { errCode, msg, data } = yield call(features.create, payload);
      if (errCode) {
        message.error(msg || '功能创建失败');
      } else {
        message.success('功能创建成功');
        yield put({ type: 'addSuccess', payload: data });
      }
    },
    *update(
      { payload }: { payload: { code: string; info: Partial<API.Feature> } },
      { call, put }: { call: Call; put: Put },
    ) {
      const { errCode, msg } = yield call(
        features.update,
        payload.code,
        payload.info,
      );
      if (errCode) {
        message.error(msg || '功能更新失败');
      } else {
        message.success('功能更新成功');
        yield put({ type: 'updateSuccess', payload });
      }
    },
    *remove(
      { payload }: { payload: { code: string } },
      { call, put }: { call: Call; put: Put },
    ) {
      const { errCode, msg } = yield call(features.remove, payload.code);
      if (errCode) {
        message.error(msg || '功能删除失败');
      } else {
        message.success('功能删除成功');
        yield put({ type: 'removeSuccess', payload });
      }
    },
  },

  reducers: {
    /** 查询列表成功后更新数据，触发渲染 */
    querySuccess(
      _state: FeatureState,
      { payload }: { payload: API.Feature[] },
    ) {
      const list = payload.map((item: API.Feature) => ({
        label: item.name,
        value: item.code,
        title: item.name,
        key: item.code,
        ...item,
      }));
      // 封装树型结构
      const treeData = convertListToTree({
        list,
        parentKey: '',
        childKey: 'children',
        fieldName: {
          parent: 'code',
          child: 'parentCode',
        },
      });
      return {
        list: payload,
        treeData,
        inited: true,
      };
    },
    addSuccess(state: FeatureState, { payload }: { payload: API.Feature }) {
      const newList = [...state.list, payload].sort(
        (a, b) => a.orderIndex - b.orderIndex,
      );
      const list = newList.map((item: API.Feature) => ({
        label: item.name,
        value: item.code,
        title: item.name,
        key: item.code,
        ...item,
      }));
      // 封装树型结构
      const treeData = convertListToTree({
        list,
        parentKey: '',
        childKey: 'children',
        fieldName: {
          parent: 'code',
          child: 'parentCode',
        },
      });
      return {
        list: newList,
        treeData,
      };
    },
    updateSuccess(
      state: FeatureState,
      { payload }: { payload: { code: string; info: Partial<API.Feature> } },
    ) {
      const newList = state.list
        .map((item) => {
          if (item.code === payload.code) {
            return { ...item, ...payload.info };
          }
          return item;
        })
        .sort((a, b) => a.orderIndex - b.orderIndex);
      const list = newList.map((item: API.Feature) => ({
        label: item.name,
        value: item.code,
        title: item.name,
        key: item.code,
        ...item,
      }));
      // 封装树型结构
      const treeData = convertListToTree({
        list,
        parentKey: '',
        childKey: 'children',
        fieldName: {
          parent: 'code',
          child: 'parentCode',
        },
      });
      return {
        list: newList,
        treeData,
      };
    },
    removeSuccess(
      state: FeatureState,
      { payload }: { payload: { code: string } },
    ) {
      const newList = state.list.filter((item) => item.code !== payload.code);
      const list = newList.map((item: API.Feature) => ({
        label: item.name,
        value: item.code,
        title: item.name,
        key: item.code,
        ...item,
      }));
      // 封装树型结构
      const treeData = convertListToTree({
        list,
        parentKey: '',
        childKey: 'children',
        fieldName: {
          parent: 'code',
          child: 'parentCode',
        },
      });
      return {
        list: newList,
        treeData,
      };
    },
  },
};

/*
 * @Description: 用户代金券
 */
import { request } from '@umijs/max';

/** 查询列表  GET /customer-coupons */
export async function index(params: Record<string, any>) {
  return request<API.ResType<{ total?: number; list?: API.CustomerCoupon[] }>>(
    '/customer-coupons',
    {
      method: 'GET',
      params,
    },
  );
}

/** 创建  POST /customer-coupons */
export async function create(body: Omit<API.CustomerCoupon, 'id'>) {
  return request<API.ResType<API.CustomerCoupon>>('/customer-coupons', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 按ID查询  GET /customer-coupons/:id */
export async function show(id: number) {
  return request<API.ResType<API.CustomerCoupon>>(`/customer-coupons/${id}`, {
    method: 'GET',
  });
}

/** 修改  PUT /customer-coupons/:id */
export async function update(id: number, body: Partial<API.CustomerCoupon>) {
  return request<API.ResType<unknown>>(`/customer-coupons/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 删除  DELETE /customer-coupons/:id */
export async function remove(id: number) {
  return request<API.ResType<unknown>>(`/customer-coupons/${id}`, {
    method: 'DELETE',
  });
}

/** 根据代金券ID获取用户列表  GET /customer-coupons/:couponId/users */
export async function getUsersByCoupon(
  couponId: number,
  params: Record<string, any>,
) {
  return request<API.ResType<{ total?: number; list?: API.CustomerCoupon[] }>>(
    `/customer-coupons/${couponId}/users`,
    {
      method: 'GET',
      params,
    },
  );
}

/** 根据用户ID获取代金券列表  GET /customer-coupons/:customerId/coupons */
export async function getCouponsByUser(
  customerId: number,
  params: Record<string, any>,
) {
  return request<API.ResType<{ total?: number; list?: API.CustomerCoupon[] }>>(
    `/customer-coupons/${customerId}/coupons`,
    {
      method: 'GET',
      params,
    },
  );
}

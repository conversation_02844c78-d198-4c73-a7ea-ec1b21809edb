// 此文件由插件自动生成，请勿手动修改
// @ts-nocheck
// prettier-ignore
// eslint-disable
import * as activity from './activity';
import * as additionalServiceOrders from './additional-service-orders';
import * as additionalService from './additional-service';
import * as area from './area';
import * as auth from './auth';
import * as banner from './banner';
import * as complaints from './complaints';
import * as coupons from './coupons';
import * as customerAddresses from './customer-addresses';
import * as customerCoupons from './customer-coupons';
import * as customerMembershipCards from './customer-membership-cards';
import * as customerStatistics from './customer-statistics';
import * as customer from './customer';
import * as customers from './customers';
import * as dataConsistency from './data-consistency';
import * as dictionaries from './dictionaries';
import * as employeeActionLogs from './employee-action-logs';
import * as employeeCheckins from './employee-checkins';
import * as employeePromotion from './employee-promotion';
import * as employees from './employees';
import * as features from './features';
import * as membershipCardOrders from './membership-card-orders';
import * as membershipCardTypes from './membership-card-types';
import * as orderSpecialNotes from './order-special-notes';
import * as order from './order';
import * as permissions from './permissions';
import * as pets from './pets';
import * as photoWalls from './photo-walls';
import * as promotionRecord from './promotion-record';
import * as revenueStatistics from './revenue-statistics';
import * as reviews from './reviews';
import * as rightsCard from './rights-card';
import * as roles from './roles';
import * as serviceDurationCalculator from './service-duration-calculator';
import * as serviceDurationStatistics from './service-duration-statistics';
import * as servicePhotos from './service-photos';
import * as serviceType from './service-type';
import * as service from './service';
import * as users from './users';
import * as vehicles from './vehicles';

export {
  activity,
  additionalServiceOrders,
  additionalService,
  area,
  auth,
  banner,
  complaints,
  coupons,
  customerAddresses,
  customerCoupons,
  customerMembershipCards,
  customerStatistics,
  customer,
  customers,
  dataConsistency,
  dictionaries,
  employeeActionLogs,
  employeeCheckins,
  employeePromotion,
  employees,
  features,
  membershipCardOrders,
  membershipCardTypes,
  orderSpecialNotes,
  order,
  permissions,
  pets,
  photoWalls,
  promotionRecord,
  revenueStatistics,
  reviews,
  rightsCard,
  roles,
  serviceDurationCalculator,
  serviceDurationStatistics,
  servicePhotos,
  serviceType,
  service,
  users,
  vehicles
};

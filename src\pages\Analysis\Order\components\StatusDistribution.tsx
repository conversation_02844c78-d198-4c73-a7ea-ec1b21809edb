import * as order from '@/services/order';
import { ProCard } from '@ant-design/pro-components';
import { Empty, Progress, message } from 'antd';
import { Dayjs } from 'dayjs';
import React, { useEffect, useState } from 'react';

interface StatusDistributionProps {
  dateRange: [Dayjs, Dayjs];
}

interface StatusData {
  status: string;
  count: number;
  percentage: string;
  totalAmount: number;
}

const StatusDistribution: React.FC<StatusDistributionProps> = ({
  dateRange,
}) => {
  const [statusData, setStatusData] = useState<StatusData[]>([]);
  const [loading, setLoading] = useState(false);

  // 获取状态分布数据
  const fetchStatusData = async () => {
    setLoading(true);
    try {
      const { errCode, msg, data } = await order.statusDistribution({
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD'),
      });

      if (errCode) {
        message.error(msg || '获取状态分布数据失败');
        return;
      }

      setStatusData(data || []);
    } catch (error) {
      console.error('获取状态分布数据失败:', error);
      message.error('获取状态分布数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStatusData();
  }, [dateRange]);

  // 渲染分布图
  const renderDistribution = () => {
    if (statusData.length === 0) {
      return <Empty description="暂无数据" />;
    }

    const colors = [
      '#1890ff',
      '#52c41a',
      '#faad14',
      '#f5222d',
      '#722ed1',
      '#13c2c2',
      '#eb2f96',
      '#fa8c16',
    ];

    return (
      <div style={{ padding: '20px 0' }}>
        {statusData.map((item, index) => (
          <div key={item.status} style={{ marginBottom: 16 }}>
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                marginBottom: 4,
              }}
            >
              <span
                style={{
                  width: 80,
                  fontSize: '14px',
                  fontWeight: 500,
                }}
              >
                {item.status}
              </span>
              <div style={{ flex: 1, marginLeft: 12, marginRight: 12 }}>
                <Progress
                  percent={parseFloat(item.percentage)}
                  strokeColor={colors[index % colors.length]}
                  showInfo={false}
                  size="small"
                />
              </div>
              <span
                style={{
                  width: 120,
                  textAlign: 'right',
                  fontSize: '12px',
                  color: '#666',
                }}
              >
                {item.count} 单 ({item.percentage}%)
              </span>
            </div>
            <div
              style={{
                fontSize: '12px',
                color: '#999',
                textAlign: 'right',
                marginTop: 2,
              }}
            >
              金额: ¥{item.totalAmount.toFixed(2)}
            </div>
          </div>
        ))}

        {/* 总计信息 */}
        <div
          style={{
            marginTop: 20,
            padding: '12px 0',
            borderTop: '1px solid #f0f0f0',
            textAlign: 'center',
            fontSize: '12px',
            color: '#999',
          }}
        >
          总订单数: {statusData.reduce((sum, item) => sum + item.count, 0)} 单
          <br />
          总金额: ¥
          {statusData
            .reduce((sum, item) => sum + item.totalAmount, 0)
            .toFixed(2)}
        </div>
      </div>
    );
  };

  return (
    <ProCard title="订单状态分布" loading={loading} style={{ height: '300px' }}>
      {renderDistribution()}
    </ProCard>
  );
};

export default StatusDistribution;

import { Descriptions, Image, Modal, Rate, Space, Tag, Typography } from 'antd';
import React from 'react';

const { Paragraph, Title, Text } = Typography;

interface ReviewDetailModalProps {
  visible: boolean;
  order: API.Order | null;
  onClose: () => void;
}

const ReviewDetailModal: React.FC<ReviewDetailModalProps> = ({
  visible,
  order,
  onClose,
}) => {
  if (!order) return null;

  const review = order.review;

  // 解析图片URLs
  const photoUrls = review?.photoURLs || [];

  // 如果没有评价，显示提示信息
  if (!review) {
    return (
      <Modal
        title="评价详情"
        open={visible}
        onCancel={onClose}
        footer={null}
        width={600}
        destroyOnClose
      >
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Title level={4} type="secondary">
            该订单暂无评价
          </Title>
          <Text type="secondary">订单编号：{order.sn}</Text>
        </div>
      </Modal>
    );
  }

  return (
    <Modal
      title="评价详情"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      destroyOnClose
    >
      <Descriptions bordered column={2} size="small">
        <Descriptions.Item label="评价ID" span={1}>
          {review.id}
        </Descriptions.Item>
        <Descriptions.Item label="订单编号" span={1}>
          {order.sn}
        </Descriptions.Item>

        <Descriptions.Item label="客户姓名" span={1}>
          {order.customer?.nickname || '未知客户'}
        </Descriptions.Item>
        <Descriptions.Item label="客户电话" span={1}>
          {order.customer?.phone || '未知电话'}
        </Descriptions.Item>

        <Descriptions.Item label="服务员工" span={1}>
          {order.employee?.name || '未知员工'}
        </Descriptions.Item>
        <Descriptions.Item label="员工等级" span={1}>
          <Tag color="blue">等级 {order.employee?.level || 0}</Tag>
        </Descriptions.Item>

        <Descriptions.Item label="服务名称" span={1}>
          {order.orderDetails?.[0]?.service?.serviceName || '未知服务'}
        </Descriptions.Item>
        <Descriptions.Item label="服务类型" span={1}>
          {order.orderDetails?.[0]?.service?.serviceType?.name || '未知类型'}
        </Descriptions.Item>

        <Descriptions.Item label="评分" span={1}>
          <Space>
            <Rate disabled value={review.rating} />
            <span>{review.rating} 分</span>
          </Space>
        </Descriptions.Item>
        <Descriptions.Item label="订单金额" span={1}>
          ¥{order.totalFee}
        </Descriptions.Item>

        <Descriptions.Item label="评价内容" span={2}>
          <Paragraph>{review.comment || '无文字评价'}</Paragraph>
        </Descriptions.Item>

        {photoUrls.length > 0 && (
          <Descriptions.Item label="评价图片" span={2}>
            <Space wrap>
              {photoUrls.map((url, index) => (
                <Image
                  key={index}
                  width={100}
                  height={100}
                  src={url.trim()}
                  style={{ objectFit: 'cover', borderRadius: '4px' }}
                  placeholder={
                    <div
                      style={{
                        width: 100,
                        height: 100,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: '#f5f5f5',
                        borderRadius: '4px',
                      }}
                    >
                      加载中...
                    </div>
                  }
                />
              ))}
            </Space>
          </Descriptions.Item>
        )}
      </Descriptions>
    </Modal>
  );
};

export default ReviewDetailModal;

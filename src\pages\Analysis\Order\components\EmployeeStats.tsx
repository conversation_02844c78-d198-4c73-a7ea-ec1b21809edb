import * as order from '@/services/order';
import { EyeOutlined, ReloadOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Avatar, Button, message, Rate, Space, Tag } from 'antd';
import { Dayjs } from 'dayjs';
import React, { useRef } from 'react';

interface EmployeeStatsProps {
  dateRange: [Dayjs, Dayjs];
}

interface EmployeeData {
  employeeId: number;
  employeeName: string;
  employeePhone: string;
  employeeAvatar?: string;
  employeeRating: number;
  orderCount: number;
  totalAmount: number;
  avgAmount: number;
}

const EmployeeStats: React.FC<EmployeeStatsProps> = ({ dateRange }) => {
  const actionRef = useRef<ActionType>();

  // 表格列定义
  const columns: ProColumns<EmployeeData>[] = [
    {
      title: '员工ID',
      dataIndex: 'employeeId',
      key: 'employeeId',
      width: 80,
      hideInSearch: true,
    },
    {
      title: '员工信息',
      dataIndex: 'employeeName',
      key: 'employeeName',
      width: 200,
      render: (_, record) => (
        <Space>
          <Avatar
            src={record.employeeAvatar}
            size="small"
            style={{ backgroundColor: '#1890ff' }}
          >
            {record.employeeName?.charAt(0)}
          </Avatar>
          <div>
            <div style={{ fontWeight: 500 }}>{record.employeeName}</div>
            <div style={{ fontSize: '12px', color: '#999' }}>
              {record.employeePhone}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: '服务评分',
      dataIndex: 'employeeRating',
      key: 'employeeRating',
      width: 120,
      align: 'center',
      hideInSearch: true,
      render: (_rating, entity) => (
        <Space direction="vertical" size={0} style={{ textAlign: 'center' }}>
          <Rate
            disabled
            value={entity.employeeRating}
            style={{ fontSize: '12px' }}
            allowHalf
          />
          <span style={{ fontSize: '12px', color: '#666' }}>
            {entity.employeeRating?.toFixed(1)} 分
          </span>
        </Space>
      ),
    },
    {
      title: '订单数量',
      dataIndex: 'orderCount',
      key: 'orderCount',
      width: 100,
      align: 'center',
      hideInSearch: true,
      sorter: true,
      render: (count) => (
        <Tag color="blue" style={{ margin: 0 }}>
          {count} 单
        </Tag>
      ),
    },
    {
      title: '总金额',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      width: 120,
      align: 'right',
      hideInSearch: true,
      sorter: true,
      render: (_amount, entity) => (
        <span style={{ fontWeight: 500, color: '#52c41a' }}>
          ¥{entity.totalAmount?.toFixed(2)}
        </span>
      ),
    },
    {
      title: '平均金额',
      dataIndex: 'avgAmount',
      key: 'avgAmount',
      width: 120,
      align: 'right',
      hideInSearch: true,
      render: (_amount, entity) => (
        <span style={{ color: '#666' }}>¥{entity.avgAmount?.toFixed(2)}</span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      fixed: 'right',
      hideInSearch: true,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => {
              // 这里可以添加查看员工详细统计的功能
              message.info(`查看员工 ${record.employeeName} 的详细统计`);
            }}
          >
            详情
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <ProTable<EmployeeData>
      actionRef={actionRef}
      rowKey="employeeId"
      headerTitle="员工绩效统计"
      columns={columns}
      search={{
        labelWidth: 'auto',
      }}
      pagination={{
        pageSize: 20,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) =>
          `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
      }}
      scroll={{ x: 800 }}
      request={async (params, sort) => {
        try {
          const { errCode, msg, data } = await order.employeeStats({
            ...params,
            startDate: dateRange[0].format('YYYY-MM-DD'),
            endDate: dateRange[1].format('YYYY-MM-DD'),
            sortBy: sort?.orderCount
              ? 'orderCount'
              : sort?.totalAmount
              ? 'totalAmount'
              : undefined,
            sortOrder: sort?.orderCount
              ? sort.orderCount === 'ascend'
                ? 'asc'
                : 'desc'
              : sort?.totalAmount
              ? sort.totalAmount === 'ascend'
                ? 'asc'
                : 'desc'
              : undefined,
          });

          if (errCode) {
            message.error(msg || '获取员工统计失败');
            return {
              data: [],
              total: 0,
              success: false,
            };
          }

          return {
            data: data?.list || [],
            total: data?.total || 0,
            success: true,
          };
        } catch (error) {
          console.error('获取员工统计失败:', error);
          message.error('获取员工统计失败');
          return {
            data: [],
            total: 0,
            success: false,
          };
        }
      }}
      toolBarRender={() => [
        <Button
          key="refresh"
          icon={<ReloadOutlined />}
          onClick={() => actionRef.current?.reload()}
        >
          刷新
        </Button>,
      ]}
    />
  );
};

export default EmployeeStats;

/*
 * @Description: 用户地址
 */
import { request } from '@umijs/max';

/** 查询用户地址列表  GET /customer-addresses */
export async function getCustomerAddresses(params: Record<string, any>) {
  return request<API.ResType<{ total?: number; list?: API.CustomerAddress[] }>>(
    '/customer-addresses',
    {
      method: 'GET',
      params,
    },
  );
}

/** 根据用户ID获取地址列表  GET /customer-addresses/customer/:customerId */
export async function getAddressesByCustomer(customerId: number) {
  return request<API.ResType<API.CustomerAddress[]>>(
    `/customer-addresses/customer/${customerId}`,
    {
      method: 'GET',
    },
  );
}

/** 按ID查询  GET /customer-addresses/:id */
export async function getCustomerAddress(id: number) {
  return request<API.ResType<API.CustomerAddress>>(
    `/customer-addresses/${id}`,
    {
      method: 'GET',
    },
  );
}

/** 创建  POST /customer-addresses */
export async function createCustomerAddress(
  body: Omit<API.CustomerAddress, 'id'>,
) {
  return request<API.ResType<API.CustomerAddress>>('/customer-addresses', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 修改  PUT /customer-addresses/:id */
export async function updateCustomerAddress(
  id: number,
  body: Partial<API.CustomerAddress>,
) {
  return request<API.ResType<unknown>>(`/customer-addresses/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 删除  DELETE /customer-addresses/:id */
export async function removeCustomerAddress(id: number) {
  return request<API.ResType<unknown>>(`/customer-addresses/${id}`, {
    method: 'DELETE',
  });
}

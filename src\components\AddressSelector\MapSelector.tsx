import GaoDeMap from '@/components/GaoDeMap';
import { reverseGeocode } from '@/components/GaoDeMap/utils';
import { Button, Input, Modal, Space, Typography, message } from 'antd';
import React, { useState } from 'react';

const { Text } = Typography;

interface MapSelectorProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (data: {
    address: string;
    addressDetail?: string;
    longitude: number;
    latitude: number;
    addressRemark?: string;
  }) => void;
}

/**
 * 地图地址选择器
 * 在地图上点击选择地址
 */
const MapSelector: React.FC<MapSelectorProps> = ({
  visible,
  onClose,
  onSelect,
}) => {
  const [selectedPosition, setSelectedPosition] = useState<{
    longitude: number;
    latitude: number;
    address: string;
  }>();
  const [addressDetail, setAddressDetail] = useState('');
  const [addressRemark, setAddressRemark] = useState('');
  const [loading, setLoading] = useState(false);

  // 处理地图点击事件
  const handleMapClick = async (e: any) => {
    const { lng, lat } = e.lnglat;
    setLoading(true);

    try {
      // 逆地理编码获取地址
      const address = await reverseGeocode(lng, lat);
      setSelectedPosition({
        longitude: lng,
        latitude: lat,
        address,
      });
      message.success('地址选择成功');
    } catch (error) {
      console.error('地址解析失败:', error);
      message.error('地址解析失败，请重新选择');
    } finally {
      setLoading(false);
    }
  };

  // 处理确认选择
  const handleConfirm = () => {
    if (!selectedPosition) {
      message.warning('请在地图上点击选择地址');
      return;
    }

    onSelect({
      address: selectedPosition.address,
      addressDetail: addressDetail.trim() || undefined,
      longitude: selectedPosition.longitude,
      latitude: selectedPosition.latitude,
      addressRemark: addressRemark.trim() || undefined,
    });
  };

  // 重置状态
  const handleClose = () => {
    setSelectedPosition(undefined);
    setAddressDetail('');
    setAddressRemark('');
    onClose();
  };

  // 生成地图标记点
  const markers = selectedPosition
    ? [
        {
          position: [selectedPosition.longitude, selectedPosition.latitude] as [
            number,
            number,
          ],
          title: '选中位置',
          content: `<div>${selectedPosition.address}</div>`,
        },
      ]
    : [];

  return (
    <Modal
      title="在地图上选择地址"
      open={visible}
      onCancel={handleClose}
      width={800}
      footer={[
        <Button key="cancel" onClick={handleClose}>
          取消
        </Button>,
        <Button
          key="confirm"
          type="primary"
          onClick={handleConfirm}
          disabled={!selectedPosition}
        >
          确认选择
        </Button>,
      ]}
    >
      <Space direction="vertical" style={{ width: '100%' }} size="middle">
        <div>
          <Text type="secondary">请在地图上点击选择服务地址</Text>
        </div>

        {selectedPosition && (
          <div
            style={{
              padding: '12px',
              backgroundColor: '#f6ffed',
              borderRadius: '6px',
            }}
          >
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <div>
                <Text strong>主地址：</Text>
                <Text>{selectedPosition.address}</Text>
              </div>
              <div>
                <Text strong>详细地址：</Text>
                <Input
                  placeholder="请输入详细地址，如门牌号、楼层、房间号等（可选）"
                  value={addressDetail}
                  onChange={(e) => setAddressDetail(e.target.value)}
                  style={{ marginTop: 4 }}
                />
              </div>
              <div>
                <Text strong>位置备注：</Text>
                <Input
                  placeholder="请输入位置备注，如最近出入口、显著标志物等（可选）"
                  value={addressRemark}
                  onChange={(e) => setAddressRemark(e.target.value)}
                  style={{ marginTop: 4 }}
                />
              </div>
              <div>
                <Text strong>经纬度：</Text>
                <Text>
                  {selectedPosition.longitude.toFixed(6)},{' '}
                  {selectedPosition.latitude.toFixed(6)}
                </Text>
              </div>
            </Space>
          </div>
        )}

        <div
          style={{
            height: '400px',
            border: '1px solid #d9d9d9',
            borderRadius: '6px',
          }}
        >
          <GaoDeMap
            city="西安"
            zoom={13}
            markers={markers}
            events={{
              onClick: handleMapClick,
            }}
            style={{ height: '100%', width: '100%' }}
          />
        </div>

        {loading && (
          <div style={{ textAlign: 'center' }}>
            <Text type="secondary">正在解析地址...</Text>
          </div>
        )}
      </Space>
    </Modal>
  );
};

export default MapSelector;

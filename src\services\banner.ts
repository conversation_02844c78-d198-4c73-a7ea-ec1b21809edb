import { request } from '@umijs/max';

/** 查询列表  GET /banners */
export async function index(params: Record<string, any>) {
  return request<API.ResType<{ total?: number; list?: API.Banner[] }>>(
    '/banners',
    {
      method: 'GET',
      params,
    },
  );
}

/** 创建  POST /banners */
export async function create(body: Omit<API.Banner, 'id'>) {
  return request<API.ResType<API.Banner>>('/banners', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 按ID查询  GET /banners/:id */
export async function show(id: number) {
  return request<API.ResType<API.Banner>>(`/banners/${id}`, {
    method: 'GET',
  });
}

/** 修改  PUT /banners/:id */
export async function update(id: number, body: Partial<API.Banner>) {
  return request<API.ResType<unknown>>(`/banners/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 删除  DELETE /banners/:id */
export async function remove(id: number) {
  return request<API.ResType<unknown>>(`/banners/${id}`, {
    method: 'DELETE',
  });
}

/** 更新轮播图优先级  PUT /banners/:id/priority */
export async function updatePriority(id: number, priority: number) {
  return request<API.ResType<unknown>>(`/banners/${id}/priority`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: { priority },
  });
}

/** 获取已启用的轮播图列表  GET /banners/enabled/list */
export async function getEnabledList() {
  return request<API.ResType<API.Banner[]>>('/banners/enabled/list', {
    method: 'GET',
  });
}

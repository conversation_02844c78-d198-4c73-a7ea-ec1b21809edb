import {
  createFromSelected,
  getAvailableServicePhotos,
} from '@/services/photo-walls';
import { EyeOutlined, PictureOutlined } from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  Empty,
  Form,
  Image,
  Input,
  InputNumber,
  message,
  Modal,
  Radio,
  Row,
  Space,
  Spin,
  Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';

const { TextArea } = Input;
const { Text } = Typography;

interface CreateFromServiceModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const CreateFromServiceModal: React.FC<CreateFromServiceModalProps> = ({
  visible,
  onClose,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [servicePhotos, setServicePhotos] = useState<API.ServicePhoto[]>([]);
  const [selectedPhoto, setSelectedPhoto] = useState<API.ServicePhoto | null>(
    null,
  );
  const [selectedBeforePhoto, setSelectedBeforePhoto] = useState<string>('');
  const [selectedAfterPhoto, setSelectedAfterPhoto] = useState<string>('');

  // 获取可用的服务照片
  const fetchServicePhotos = async (params = {}) => {
    setLoading(true);
    try {
      const { errCode, msg, data } = await getAvailableServicePhotos(params);
      if (errCode) {
        message.error(msg || '获取服务照片失败');
      } else {
        setServicePhotos(data?.list || []);
      }
    } catch (error) {
      console.error('获取服务照片失败:', error);
      message.error('获取服务照片失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible) {
      fetchServicePhotos();
    }
  }, [visible]);

  // 选择服务照片记录
  const handleSelectPhoto = (photo: API.ServicePhoto) => {
    setSelectedPhoto(photo);
    // 默认选择第一张照片
    if (photo.beforePhotos && photo.beforePhotos.length > 0) {
      setSelectedBeforePhoto(photo.beforePhotos[0]);
    }
    if (photo.afterPhotos && photo.afterPhotos.length > 0) {
      setSelectedAfterPhoto(photo.afterPhotos[0]);
    }

    // 自动填充表单
    form.setFieldsValue({
      title: `${photo.order?.customer?.nickname || ''}的${
        photo.order?.orderDetails?.[0]?.service?.serviceName || ''
      }服务`,
      description: '专业服务，效果显著',
      priority: 10,
    });
  };

  // 提交创建
  const handleSubmit = async () => {
    if (!selectedPhoto) {
      message.error('请先选择一个服务照片记录');
      return;
    }

    if (!selectedBeforePhoto || !selectedAfterPhoto) {
      message.error('请选择服务前后照片');
      return;
    }

    try {
      const values = await form.validateFields();
      const { errCode, msg } = await createFromSelected({
        servicePhotoId: selectedPhoto.id,
        selectedBeforePhoto,
        selectedAfterPhoto,
        title: values.title,
        description: values.description,
        priority: values.priority || 10,
      });

      if (errCode) {
        message.error(msg || '创建失败');
      } else {
        message.success('创建成功');
        onSuccess();
      }
    } catch (error) {
      message.error('创建失败');
    }
  };

  // 渲染服务照片卡片
  const renderPhotoCard = (photo: API.ServicePhoto) => (
    <Card
      key={photo.id}
      size="small"
      hoverable
      style={{
        marginBottom: 16,
        border:
          selectedPhoto?.id === photo.id
            ? '2px solid #1890ff'
            : '1px solid #d9d9d9',
      }}
      onClick={() => handleSelectPhoto(photo)}
    >
      <Row gutter={16}>
        <Col span={8}>
          <div style={{ textAlign: 'center' }}>
            <Text strong>服务前照片</Text>
            <div style={{ marginTop: 8 }}>
              {photo.beforePhotos && photo.beforePhotos.length > 0 ? (
                <Space wrap>
                  {photo.beforePhotos.map((url, index) => (
                    <Image
                      key={index}
                      src={url}
                      width={60}
                      height={60}
                      style={{ objectFit: 'cover', borderRadius: 4 }}
                      preview={{ mask: <EyeOutlined /> }}
                    />
                  ))}
                </Space>
              ) : (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description="无照片"
                />
              )}
            </div>
          </div>
        </Col>
        <Col span={8}>
          <div style={{ textAlign: 'center' }}>
            <Text strong>服务后照片</Text>
            <div style={{ marginTop: 8 }}>
              {photo.afterPhotos && photo.afterPhotos.length > 0 ? (
                <Space wrap>
                  {photo.afterPhotos.map((url, index) => (
                    <Image
                      key={index}
                      src={url}
                      width={60}
                      height={60}
                      style={{ objectFit: 'cover', borderRadius: 4 }}
                      preview={{ mask: <EyeOutlined /> }}
                    />
                  ))}
                </Space>
              ) : (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description="无照片"
                />
              )}
            </div>
          </div>
        </Col>
        <Col span={8}>
          <div>
            <div>
              <Text strong>订单号：</Text>
              {photo.order?.sn}
            </div>
            <div>
              <Text strong>客户：</Text>
              {photo.order?.customer?.nickname}
            </div>
            <div>
              <Text strong>服务人员：</Text>
              {photo.employee?.name}
            </div>
            <div>
              <Text strong>服务：</Text>
              {photo.order?.orderDetails?.[0]?.service?.serviceName}
            </div>
          </div>
        </Col>
      </Row>
    </Card>
  );

  return (
    <Modal
      title="从服务照片创建照片墙"
      open={visible}
      onCancel={onClose}
      width={1200}
      footer={[
        <Button key="cancel" onClick={onClose}>
          取消
        </Button>,
        <Button key="submit" type="primary" onClick={handleSubmit}>
          创建照片墙
        </Button>,
      ]}
      destroyOnClose
    >
      <Row gutter={16}>
        {/* 左侧：服务照片列表 */}
        <Col span={14}>
          <Card title="选择服务照片记录" size="small">
            <Spin spinning={loading}>
              {servicePhotos.length > 0 ? (
                <div style={{ maxHeight: 400, overflowY: 'auto' }}>
                  {servicePhotos.map(renderPhotoCard)}
                </div>
              ) : (
                <Empty
                  image={
                    <PictureOutlined
                      style={{ fontSize: 48, color: '#d9d9d9' }}
                    />
                  }
                  description="暂无可用的服务照片"
                />
              )}
            </Spin>
          </Card>
        </Col>

        {/* 右侧：照片选择和信息填写 */}
        <Col span={10}>
          {selectedPhoto && (
            <Card title="照片选择和信息设置" size="small">
              <Form form={form} layout="vertical">
                {/* 服务前照片选择 */}
                {selectedPhoto.beforePhotos &&
                  selectedPhoto.beforePhotos.length > 1 && (
                    <Form.Item label="选择服务前照片">
                      <Radio.Group
                        value={selectedBeforePhoto}
                        onChange={(e) => setSelectedBeforePhoto(e.target.value)}
                      >
                        <Space wrap>
                          {selectedPhoto.beforePhotos.map((url, index) => (
                            <Radio.Button key={index} value={url}>
                              <Image
                                src={url}
                                width={40}
                                height={40}
                                style={{ objectFit: 'cover', borderRadius: 4 }}
                                preview={false}
                              />
                            </Radio.Button>
                          ))}
                        </Space>
                      </Radio.Group>
                    </Form.Item>
                  )}

                {/* 服务后照片选择 */}
                {selectedPhoto.afterPhotos &&
                  selectedPhoto.afterPhotos.length > 1 && (
                    <Form.Item label="选择服务后照片">
                      <Radio.Group
                        value={selectedAfterPhoto}
                        onChange={(e) => setSelectedAfterPhoto(e.target.value)}
                      >
                        <Space wrap>
                          {selectedPhoto.afterPhotos.map((url, index) => (
                            <Radio.Button key={index} value={url}>
                              <Image
                                src={url}
                                width={40}
                                height={40}
                                style={{ objectFit: 'cover', borderRadius: 4 }}
                                preview={false}
                              />
                            </Radio.Button>
                          ))}
                        </Space>
                      </Radio.Group>
                    </Form.Item>
                  )}

                <Form.Item
                  name="title"
                  label="照片标题"
                  rules={[{ required: true, message: '请输入照片标题' }]}
                >
                  <Input placeholder="请输入照片标题" />
                </Form.Item>

                <Form.Item name="description" label="照片描述">
                  <TextArea rows={3} placeholder="请输入照片描述" />
                </Form.Item>

                <Form.Item name="priority" label="展示优先级">
                  <InputNumber
                    min={0}
                    max={999}
                    placeholder="请输入优先级"
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Form>
            </Card>
          )}
        </Col>
      </Row>
    </Modal>
  );
};

export default CreateFromServiceModal;

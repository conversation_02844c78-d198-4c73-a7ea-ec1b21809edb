import { getUsersByCoupon, remove } from '@/services/customer-coupons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, Drawer, message, Popconfirm, Space } from 'antd';
import moment from 'moment';
import React, { useRef, useState } from 'react';
import IssueCouponModal from '../components/IssueCouponModal';
import UsageRecordList from '../components/UsageRecordList';

interface UserListProps {
  couponId: number;
}

/**
 * 代金券用户列表组件
 */
const UserList: React.FC<UserListProps> = ({ couponId }) => {
  const actionRef = useRef<ActionType>();

  // 发放代金券模态框状态
  const [issueModalVisible, setIssueModalVisible] = useState<boolean>(false);
  const [selectedCustomerId, setSelectedCustomerId] = useState<number>(0);

  // 使用记录抽屉状态
  const [usageRecordDrawerVisible, setUsageRecordDrawerVisible] =
    useState<boolean>(false);
  const [currentCustomerCouponId, setCurrentCustomerCouponId] =
    useState<number>(0);
  const [currentUserName, setCurrentUserName] = useState<string>('');

  // 处理撤销发放
  const handleRevoke = async (id: number) => {
    try {
      const response = await remove(id);
      if (response.errCode) {
        message.error(response.msg || '撤销发放失败');
      } else {
        message.success('撤销发放成功');
        actionRef.current?.reload();
      }
    } catch (error) {
      console.error('撤销发放失败', error);
      message.error('撤销发放失败，请重试');
    }
  };

  // 处理查看使用记录
  const handleViewUsageRecords = (record: API.CustomerCoupon) => {
    setCurrentCustomerCouponId(record.id);
    setCurrentUserName(record.customer?.nickname || `用户${record.customerId}`);
    setUsageRecordDrawerVisible(true);
  };

  // 表格列定义
  const columns: ProColumns<API.CustomerCoupon>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      search: false,
    },
    {
      title: '用户ID',
      dataIndex: ['customer', 'id'],
      width: 100,
      hidden: true,
      search: false,
    },
    {
      title: '用户手机号',
      dataIndex: ['customer', 'phone'],
      width: 150,
    },
    {
      title: '用户昵称',
      dataIndex: ['customer', 'nickname'],
      width: 150,
      render: (_, record) => record.customer?.nickname || '-',
    },
    {
      title: '领取时间',
      dataIndex: 'receiveTime',
      width: 180,
      valueType: 'dateTime',
      search: false,
      render: (_, record) =>
        record.receiveTime
          ? moment(record.receiveTime).format('YYYY-MM-DD HH:mm:ss')
          : '-',
    },
    {
      title: '到期时间',
      dataIndex: 'expiryTime',
      width: 180,
      valueType: 'dateTime',
      search: false,
      render: (_, record) =>
        record.expiryTime
          ? moment(record.expiryTime).format('YYYY-MM-DD HH:mm:ss')
          : '-',
    },
    {
      title: '最后使用时间',
      dataIndex: 'lastUseTime',
      width: 180,
      valueType: 'dateTime',
      search: false,
      render: (_, record) =>
        record.lastUseTime
          ? moment(record.lastUseTime).format('YYYY-MM-DD HH:mm:ss')
          : '-',
    },
    {
      title: '剩余次数',
      dataIndex: 'remainTimes',
      width: 100,
      search: false,
      render: (_, record) =>
        record.remainTimes === -1 ? '不限' : record.remainTimes,
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      valueEnum: {
        active: { text: '有效', status: 'Success' },
        expired: { text: '已过期', status: 'Error' },
        used: { text: '已使用', status: 'Warning' },
      },
    },
    {
      title: '操作',
      valueType: 'option',
      width: 120,
      align: 'center',
      fixed: 'right',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            onClick={() => handleViewUsageRecords(record)}
            disabled={
              record.status === 'active' || !record.usageRecords?.length
            }
          >
            查看记录
          </Button>
          <Popconfirm
            title="确定要撤销此代金券吗？"
            description="撤销后将无法恢复，用户将无法使用此代金券"
            onConfirm={() => handleRevoke(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger>
              撤销发放
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<API.CustomerCoupon>
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        search={{
          labelWidth: 'auto',
        }}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
        }}
        options={false}
        request={async (params) => {
          if (!couponId) {
            return {
              data: [],
              success: true,
              total: 0,
            };
          }

          const response = await getUsersByCoupon(couponId, params);

          if (response.errCode) {
            message.error(response.msg || '获取用户列表失败');
            return {
              data: [],
              success: false,
              total: 0,
            };
          }

          return {
            data: response.data?.list || [],
            success: true,
            total: response.data?.total || 0,
          };
        }}
        toolBarRender={() => [
          <Button
            key="issue"
            type="primary"
            onClick={() => {
              setSelectedCustomerId(0);
              setIssueModalVisible(true);
            }}
          >
            发放代金券
          </Button>,
        ]}
      />

      {/* 发放代金券模态框 */}
      <IssueCouponModal
        open={issueModalVisible}
        onClose={() => setIssueModalVisible(false)}
        onSuccess={() => {
          setIssueModalVisible(false);
          actionRef.current?.reload();
        }}
        couponId={couponId}
        customerId={selectedCustomerId}
      />

      {/* 使用记录抽屉 */}
      <Drawer
        title={`${currentUserName} 代金券使用记录`}
        width={800}
        open={usageRecordDrawerVisible}
        onClose={() => setUsageRecordDrawerVisible(false)}
        destroyOnClose
      >
        <UsageRecordList customerCouponId={currentCustomerCouponId} />
      </Drawer>
    </>
  );
};

export default UserList;

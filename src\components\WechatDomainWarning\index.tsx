import { Alert } from 'antd';
import React from 'react';

interface WechatDomainWarningProps {
  /** 是否显示警告 */
  visible?: boolean;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 自定义类名 */
  className?: string;
  /** 是否显示为紧凑模式 */
  compact?: boolean;
}

/**
 * 微信小程序域名限制警告组件
 * 用于提醒用户输入的链接域名必须在微信小程序业务域名白名单中
 */
const WechatDomainWarning: React.FC<WechatDomainWarningProps> = ({
  visible = true,
  style,
  className,
  compact = false,
}) => {
  if (!visible) return null;

  return (
    <Alert
      message="微信小程序域名限制提醒"
      description={
        <div>
          <div style={{ marginBottom: compact ? 4 : 8 }}>
            <strong>重要：</strong>该链接域名必须在微信小程序后台的
            <strong style={{ color: '#ff4d4f' }}>业务域名白名单</strong>
            中配置，否则无法在小程序中正常加载。
          </div>
          {!compact && (
            <div style={{ fontSize: '12px', color: '#666' }}>
              配置路径：微信公众平台 → 开发管理 → 开发设置 → 业务域名
            </div>
          )}
        </div>
      }
      type="warning"
      showIcon
      style={{ marginBottom: 0, ...style }}
      className={className}
    />
  );
};

export default WechatDomainWarning;

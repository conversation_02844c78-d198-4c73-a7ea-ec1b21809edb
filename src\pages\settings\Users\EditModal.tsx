import ProFormImg from '@/components/ProFormItem/ProFormImg';
import {
  ModalForm,
  ProForm,
  ProFormSegmented,
  ProFormText,
} from '@ant-design/pro-components';
import { Form } from 'antd';
import React, { useEffect } from 'react';

const EditModal: React.FC<{
  open: boolean;
  info?: API.User;
  onSave: (info: API.User) => Promise<void>;
  onClose: () => void;
}> = ({ open, info, onSave, onClose }) => {
  const [form] = Form.useForm();

  // 当模态框打开时重置表单
  useEffect(() => {
    if (open) {
      if (info) {
        // 编辑模式：设置表单值
        form.setFieldsValue({
          ...info,
          password: '********', // 编辑时密码显示为星号
          isActive: info.isActive ? '1' : '0', // 转换布尔值为字符串
        });
      } else {
        // 新增模式：重置表单
        form.resetFields();
        form.setFieldsValue({
          isActive: '1', // 默认启用
        });
      }
    }
  }, [open, info, form]);

  return (
    <ModalForm<API.User>
      title={info ? '编辑用户' : '注册用户'}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
      }}
      open={open}
      layout="horizontal"
      grid
      labelCol={{ flex: '7em' }}
      onFinish={async (info) => {
        onSave(info);
      }}
      form={form}
    >
      <ProFormText name="id" label="ID" hidden />
      <ProForm.Group colProps={{ span: 12 }}>
        <ProFormText
          name="username"
          label="用户名"
          rules={[{ required: true, message: '请输入用户名！' }]}
        />
        <ProFormText.Password
          name="password"
          label="密码"
          rules={[{ required: true, message: '请输入密码！' }]}
          transform={(value: string) => {
            if (value === '********') {
              return undefined;
            }
            return value;
          }}
        />
        <ProFormText name="email" label="邮箱" />
      </ProForm.Group>
      <ProForm.Group colProps={{ span: 12 }}>
        <ProFormImg name="avatar" label="头像" />
      </ProForm.Group>
      <ProFormText
        name="nickname"
        label="昵称"
        rules={[{ required: true, message: '请输入昵称！' }]}
        colProps={{ span: 12 }}
      />
      <ProFormSegmented
        name="isActive"
        label="状态"
        valueEnum={{
          '0': '禁用',
          '1': '启用',
        }}
        colProps={{ span: 12 }}
        transform={(value: string | API.User['isActive']) => {
          if (typeof value === 'boolean') {
            return value;
          }
          return value === '1';
        }}
      />
    </ModalForm>
  );
};

export default EditModal;

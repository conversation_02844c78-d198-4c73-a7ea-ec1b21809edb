import ProFormImgMultiple from '@/components/ProFormItem/ProFormImgMultiple';
import { ComplaintCategory, ComplaintSubCategory } from '@/constants/complaint';
import { complaints } from '@/services';
import type { ProFormInstance } from '@ant-design/pro-components';
import {
  ModalForm,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Alert, message } from 'antd';
import React, { useRef } from 'react';

interface OrderComplaintModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
  /** 订单信息 */
  order?: API.Order;
}

const OrderComplaintModal: React.FC<OrderComplaintModalProps> = ({
  visible,
  onClose,
  onSuccess,
  order,
}) => {
  const formRef = useRef<ProFormInstance>();
  const { initialState } = useModel('@@initialState');

  /** 提交表单 */
  const handleSubmit = async (values: any) => {
    if (!order) {
      message.error('订单信息不存在');
      return false;
    }

    try {
      const submitData = {
        category: ComplaintCategory.投诉 as API.ComplaintCategory,
        subCategory: ComplaintSubCategory.订单投诉 as API.ComplaintSubCategory,
        title: values.title,
        content: values.content,
        customerId: order.customerId,
        orderId: order.id,
        employeeId: order.employeeId,
        contactInfo: values.contactInfo,
        photoURLs: values.photoURLs || [],
        createdBy: initialState?.id,
        adminNote: values.adminNote,
      };

      const response = await complaints.create(submitData);
      if (response.errCode) {
        message.error(response.msg || '录入失败');
        return false;
      }

      message.success('订单投诉录入成功');
      onSuccess();
      return true;
    } catch (error) {
      message.error('录入失败，请重试');
      return false;
    }
  };

  return (
    <ModalForm
      formRef={formRef}
      title="录入订单投诉"
      open={visible}
      onFinish={handleSubmit}
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
        width: 700,
      }}
      layout="horizontal"
      labelCol={{ span: 6 }}
      wrapperCol={{ span: 18 }}
    >
      {order && (
        <Alert
          message="订单信息"
          description={
            <div>
              <div>订单编号：{order.sn}</div>
              <div>
                客户：{order.customer?.nickname} ({order.customer?.phone})
              </div>
              {order.employee && (
                <div>
                  服务员工：{order.employee.name} ({order.employee.phone})
                </div>
              )}
              <div>
                服务时间：
                {order.serviceTime
                  ? new Date(order.serviceTime).toLocaleString()
                  : '未设置'}
              </div>
            </div>
          }
          type="info"
          style={{ marginBottom: 16 }}
        />
      )}

      <ProFormText
        name="title"
        label="投诉标题"
        rules={[
          { required: true, message: '请输入投诉标题' },
          { max: 200, message: '标题不能超过200个字符' },
        ]}
        placeholder="请简要描述投诉问题"
      />

      <ProFormTextArea
        name="content"
        label="投诉内容"
        rules={[
          { required: true, message: '请输入投诉内容' },
          { max: 2000, message: '内容不能超过2000个字符' },
        ]}
        fieldProps={{
          rows: 4,
          placeholder:
            '请详细描述投诉的具体情况，包括问题发生的时间、地点、具体情况等',
        }}
      />

      <ProFormText
        name="contactInfo"
        label="联系方式"
        fieldProps={{
          placeholder: '可选，客户联系方式（如与订单客户不同）',
          maxLength: 100,
        }}
      />

      <ProFormImgMultiple
        name="photoURLs"
        label="相关图片2"
        dir="complaint"
        maxCount={6}
        maxSize={{
          size: 2 * 1024 * 1024,
          message: '图片大小不能超过2M',
        }}
        extra="最多上传6张图片，支持jpg、png格式"
      />

      <ProFormTextArea
        name="adminNote"
        label="管理员备注"
        fieldProps={{
          rows: 2,
          placeholder: '可选，管理员录入时的备注信息，如投诉来源、处理要求等',
          maxLength: 500,
        }}
      />
    </ModalForm>
  );
};

export default OrderComplaintModal;

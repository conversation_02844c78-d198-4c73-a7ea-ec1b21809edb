import { getOrderSpecialNote } from '@/services/order-special-notes';
import { UserOutlined } from '@ant-design/icons';
import {
  Avatar,
  Card,
  Col,
  Empty,
  Image,
  Modal,
  Row,
  Space,
  Spin,
  Tag,
  Typography,
} from 'antd';
import React, { useEffect, useState } from 'react';

const { Text, Paragraph } = Typography;

interface SpecialNotesModalProps {
  open: boolean;
  order?: API.Order;
  onClose: () => void;
}

const SpecialNotesModal: React.FC<SpecialNotesModalProps> = ({
  open,
  order,
  onClose,
}) => {
  const [loading, setLoading] = useState(false);
  const [specialNote, setSpecialNote] = useState<API.OrderSpecialNote | null>(
    null,
  );

  const fetchSpecialNote = async () => {
    if (!order?.id) return;

    setLoading(true);
    try {
      const { errCode, data } = await getOrderSpecialNote(order.id);
      if (errCode === 0 && data) {
        setSpecialNote(data);
      } else {
        setSpecialNote(null);
      }
    } catch (error) {
      console.error('获取特殊情况说明失败:', error);
      setSpecialNote(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (open && order?.id) {
      fetchSpecialNote();
    }
  }, [open, order?.id]);

  const handleClose = () => {
    setSpecialNote(null);
    onClose();
  };

  return (
    <Modal
      title={
        <Space>
          <span>特殊情况说明</span>
          {order && (
            <Tag color="blue" style={{ marginLeft: 8 }}>
              {order.sn}
            </Tag>
          )}
        </Space>
      }
      open={open}
      onCancel={handleClose}
      footer={null}
      width={800}
      destroyOnClose
    >
      {loading ? (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
        </div>
      ) : specialNote ? (
        <div>
          {/* 员工信息 */}
          <Card size="small" style={{ marginBottom: 16 }}>
            <Space>
              <Avatar icon={<UserOutlined />} />
              <div>
                <Text strong>{specialNote.employee?.name || '未知员工'}</Text>
                <br />
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {specialNote.employee?.phone || ''}
                </Text>
              </div>
              <div style={{ marginLeft: 'auto' }}>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {specialNote.createdAt
                    ? new Date(specialNote.createdAt).toLocaleString()
                    : ''}
                </Text>
              </div>
            </Space>
          </Card>

          {/* 特殊情况说明内容 */}
          <Card title="说明内容" size="small" style={{ marginBottom: 16 }}>
            <Paragraph style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
              {specialNote.content}
            </Paragraph>
          </Card>

          {/* 特殊情况图片 */}
          {specialNote.photos && specialNote.photos.length > 0 && (
            <Card title="相关图片" size="small">
              <Row gutter={[16, 16]}>
                {specialNote.photos.map((photo, index) => (
                  <Col span={8} key={index}>
                    <Image
                      src={photo}
                      alt={`特殊情况图片 ${index + 1}`}
                      style={{
                        width: '100%',
                        height: '120px',
                        objectFit: 'cover',
                        borderRadius: '6px',
                      }}
                      placeholder={
                        <div
                          style={{
                            width: '100%',
                            height: '120px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: '#f5f5f5',
                            borderRadius: '6px',
                          }}
                        >
                          <Spin />
                        </div>
                      }
                    />
                  </Col>
                ))}
              </Row>
            </Card>
          )}
        </div>
      ) : (
        <Empty
          description="该订单暂无特殊情况说明"
          style={{ padding: '50px' }}
        />
      )}
    </Modal>
  );
};

export default SpecialNotesModal;

import { ComplaintStatus, StatusConfig } from '@/constants/complaint';
import { users } from '@/services';
import type { ProFormInstance } from '@ant-design/pro-components';
import {
  ModalForm,
  ProFormRadio,
  ProFormSelect,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Alert, message, Space, Tag } from 'antd';
import React, { useRef } from 'react';

interface ComplaintHandleModalProps {
  visible: boolean;
  complaint?: API.Complaint;
  onSubmit: (values: {
    status: API.ComplaintStatus;
    result?: string;
    handlerId: number;
  }) => Promise<void>;
  onClose: () => void;
}

const ComplaintHandleModal: React.FC<ComplaintHandleModalProps> = ({
  visible,
  complaint,
  onSubmit,
  onClose,
}) => {
  const formRef = useRef<ProFormInstance>();

  if (!complaint) return null;

  // 根据当前状态获取可选的处理选项
  const getAvailableActions = (currentStatus: API.ComplaintStatus) => {
    const actions = [];

    switch (currentStatus) {
      case ComplaintStatus.待处理:
        actions.push(
          {
            label: '开始处理',
            value: ComplaintStatus.处理中,
            description: '将状态更改为处理中，开始处理此投诉建议',
            defaultResult: '管理员已开始处理此投诉建议，正在核实相关情况。',
          },
          {
            label: '直接关闭',
            value: ComplaintStatus.已关闭,
            description: '直接关闭此投诉建议（如重复投诉、无效投诉等）',
            defaultResult:
              '经核实，此投诉建议为重复投诉或无效投诉，已直接关闭处理。如有疑问，请重新提交。',
          },
        );
        break;
      case ComplaintStatus.处理中:
        actions.push(
          {
            label: '标记为已解决',
            value: ComplaintStatus.已解决,
            description: '问题已经处理完成，等待确认',
            defaultResult: '', // 这个需要根据实际情况填写，不提供默认值
          },
          {
            label: '处理后关闭',
            value: ComplaintStatus.已关闭,
            description: '处理完成后直接关闭流程',
            defaultResult:
              '投诉建议已处理完成，相关问题已得到妥善解决，流程关闭。感谢您的反馈。',
          },
        );
        break;
      case ComplaintStatus.已解决:
        actions.push({
          label: '确认关闭',
          value: ComplaintStatus.已关闭,
          description: '确认问题已解决，关闭流程',
          defaultResult:
            '经确认，投诉建议中反映的问题已得到妥善解决，流程正式关闭。',
        });
        break;
      default:
        break;
    }

    return actions;
  };

  const availableActions = getAvailableActions(complaint.status);
  const currentStatusConfig = StatusConfig[complaint.status];

  // 获取默认处理结果
  const getDefaultResult = (status: string) => {
    const action = availableActions.find((action) => action.value === status);
    return action?.defaultResult || '';
  };

  return (
    <ModalForm<{
      status: API.ComplaintStatus;
      result?: string;
      handlerId: number;
    }>
      formRef={formRef}
      title="处理投诉建议"
      open={visible}
      onFinish={async (values) => {
        await onSubmit(values);
        return true;
      }}
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
      }}
      layout="horizontal"
      labelCol={{ span: 6 }}
      wrapperCol={{ span: 18 }}
      initialValues={{
        status:
          availableActions.length > 0
            ? availableActions[0].value
            : complaint.status,
        handlerId: complaint.handlerId,
        result:
          availableActions.length > 0
            ? getDefaultResult(availableActions[0].value)
            : complaint.result,
      }}
      onValuesChange={(changedValues) => {
        // 当处理状态改变时，自动更新处理结果
        if (changedValues.status && formRef.current) {
          const defaultResult = getDefaultResult(changedValues.status);
          if (defaultResult) {
            formRef.current.setFieldsValue({ result: defaultResult });
          }
        }
      }}
    >
      {/* 当前状态显示 */}
      <Alert
        message={
          <Space>
            <span>当前状态：</span>
            <Tag color={currentStatusConfig.color}>
              {currentStatusConfig.text}
            </Tag>
          </Space>
        }
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      {/* 处理选项 */}
      {availableActions.length > 0 ? (
        <ProFormRadio.Group
          name="status"
          label="处理操作"
          rules={[{ required: true, message: '请选择处理操作' }]}
          options={availableActions.map((action) => ({
            label: (
              <div>
                <div style={{ fontWeight: 'bold' }}>{action.label}</div>
                <div
                  style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}
                >
                  {action.description}
                </div>
              </div>
            ),
            value: action.value,
          }))}
        />
      ) : (
        <Alert
          message="当前状态已是最终状态，无法进行进一步处理"
          type="warning"
          showIcon
          style={{ marginBottom: 24 }}
        />
      )}

      <ProFormSelect
        name="handlerId"
        label="处理人员"
        rules={[{ required: true, message: '请选择处理人员' }]}
        request={async () => {
          try {
            const response = await users.index({ pageSize: 100 });
            if (response.errCode) {
              message.error(response.msg || '获取用户列表失败');
              return [];
            }
            return (response.data?.list || []).map((user: API.User) => ({
              label: user.nickname || user.username,
              value: user.id,
            }));
          } catch (error) {
            message.error('获取用户列表失败');
            return [];
          }
        }}
        showSearch
        placeholder="请选择处理人员"
      />

      <ProFormTextArea
        name="result"
        label="处理结果"
        placeholder="请输入处理结果说明..."
        extra="💡 提示：系统已根据您选择的处理操作自动填充默认处理结果，您可以根据实际情况进行修改"
        fieldProps={{
          rows: 4,
          maxLength: 500,
          showCount: true,
        }}
        rules={[
          ({ getFieldValue }: any) => ({
            validator: (_: any, value: string) => {
              // 如果状态是已解决或已关闭，则处理结果为必填
              const status = getFieldValue('status');
              if (
                (status === ComplaintStatus.已解决 ||
                  status === ComplaintStatus.已关闭) &&
                !value?.trim()
              ) {
                return Promise.reject(
                  new Error('当选择"已解决"或"已关闭"时，处理结果为必填项'),
                );
              }
              return Promise.resolve();
            },
          }),
        ]}
      />
    </ModalForm>
  );
};

export default ComplaintHandleModal;

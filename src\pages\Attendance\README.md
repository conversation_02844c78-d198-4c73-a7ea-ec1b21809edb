# 员工出车拍照管理

## 功能概述

员工出车拍照管理是一个专门为管理端提供的员工打卡监控系统，允许管理员查看和管理员工出车时的拍照打卡记录。

## 主要功能

### 1. 打卡记录管理

- **记录列表**：查看所有员工的打卡记录
- **详情查看**：查看打卡的详细信息，包括照片、位置、描述等
- **记录删除**：删除不当的打卡记录
- **批量操作**：支持批量删除多条记录

### 2. 筛选和搜索

- **员工筛选**：按员工筛选打卡记录
- **时间筛选**：按打卡时间范围筛选
- **关键词搜索**：支持多种条件组合搜索

### 3. 统计分析

- **总体统计**：显示总打卡次数、今日打卡次数等
- **时间统计**：本周、本月打卡统计
- **员工统计**：活跃员工数、今日活跃员工数
- **平均统计**：平均每日打卡、员工平均打卡

## 技术实现

### 组件结构

```
src/pages/Attendance/
├── index.tsx                    # 主入口文件
├── components/
│   ├── index.ts                # 组件导出
│   ├── CheckinList.tsx         # 打卡记录列表
│   ├── CheckinStatistics.tsx   # 打卡统计
│   └── CheckinDetailModal.tsx  # 打卡详情模态框
└── README.md                   # 功能说明
```

### API 接口

- `employeeCheckins.index()`: 获取打卡记录列表
- `employeeCheckins.remove()`: 删除单个记录
- `employeeCheckins.batchRemove()`: 批量删除记录
- `employeeCheckins.getStatistics()`: 获取统计数据

### 数据结构

```typescript
interface CheckInPhotos {
  vehicleExterior?: string[]; // 车辆外观照片，最多9张
  serviceStaff?: string[]; // 服务人员照片，最多9张
  vehicleInterior?: string[]; // 车内情况照片，最多9张
}

interface EmployeeCheckin {
  id: number; // 打卡记录ID
  employeeId: number; // 员工ID
  photos: CheckInPhotos; // 分组照片对象
  description?: string; // 打卡描述
  address?: string; // 打卡地址
  longitude?: string; // 经度（字符串格式）
  latitude?: string; // 纬度（字符串格式）
  checkInTime: Date; // 打卡时间
  employee?: Employee; // 关联的员工信息
  createdAt?: Date; // 创建时间
  updatedAt?: Date; // 更新时间
}

interface Employee {
  id: number; // 员工ID
  name: string; // 真实姓名
  phone: string; // 手机号
  avatar?: string; // 头像
  level?: number; // 接单等级（1-5级）
  rating?: number; // 服务评分（0-5分）
}
```

## 使用说明

### 查看打卡记录

1. 进入"员工出车拍照"页面
2. 在"打卡记录"标签页查看所有记录
3. 使用筛选条件缩小查看范围
4. 点击"查看详情"按钮查看完整信息

### 管理打卡记录

1. 选择需要删除的记录
2. 点击"删除"按钮删除单个记录
3. 或选择多条记录进行批量删除

### 查看统计信息

1. 切换到"统计分析"标签页
2. 选择统计时间范围
3. 查看各项统计指标

## 界面特性

### 打卡记录列表

- **员工信息**：显示员工头像、姓名、手机号、接单等级、服务评分
- **照片预览**：按分组显示照片数量（车辆外观、服务人员、车内情况）和缩略图
- **位置信息**：显示打卡地址和坐标
- **时间显示**：格式化显示打卡时间
- **操作按钮**：查看详情、删除记录

### 打卡详情

- **完整信息**：显示所有打卡相关信息
- **分组照片查看**：按车辆外观、服务人员、车内情况分组展示照片，支持预览和放大查看
- **位置展示**：显示详细地址和经纬度坐标
- **时间记录**：显示打卡时间、创建时间、更新时间

### 统计分析

- **卡片展示**：使用统计卡片展示各项指标
- **时间选择**：支持自定义统计时间范围
- **实时更新**：数据实时刷新
- **图标美化**：使用图标增强视觉效果

## 权限控制

- 管理员可以查看所有员工的打卡记录
- 支持删除不当记录的权限控制
- 批量操作需要确认提示

## 扩展功能

### 可能的扩展方向

1. **导出功能**：支持导出打卡记录到 Excel
2. **地图显示**：在地图上显示打卡位置
3. **推送通知**：员工打卡时推送通知
4. **异常检测**：检测异常打卡行为
5. **报表生成**：生成打卡统计报表

## 注意事项

1. **照片加载**：大量照片可能影响页面加载速度
2. **数据量**：大量数据时建议分页加载
3. **权限验证**：确保只有授权用户可以访问
4. **数据备份**：删除操作不可恢复，需谨慎操作

import ProFormImgMultiple from '@/components/ProFormItem/ProFormImgMultiple';
import {
  ComplaintCategory,
  ComplaintCategoryOptions,
  ComplaintSubCategory,
  ComplaintSubCategoryOptions,
} from '@/constants/complaint';
import { complaints } from '@/services';
import { getCustomers } from '@/services/customers';
import { index as getEmployees } from '@/services/employees';
import { index as getOrders } from '@/services/order';
import type { ProFormInstance } from '@ant-design/pro-components';
import {
  ModalForm,
  ProFormDependency,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { message } from 'antd';
import React, { useRef, useState } from 'react';

interface ComplaintCreateModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
  /** 预设的订单ID（从预约列表录入时使用） */
  orderId?: number;
  /** 预设的客户ID */
  customerId?: number;
}

const ComplaintCreateModal: React.FC<ComplaintCreateModalProps> = ({
  visible,
  onClose,
  onSuccess,
  orderId,
  customerId,
}) => {
  const formRef = useRef<ProFormInstance>();
  const { initialState } = useModel('@@initialState');
  const [subCategoryOptions, setSubCategoryOptions] = useState(
    ComplaintSubCategoryOptions,
  );

  /** 根据大类筛选小类选项 */
  const handleCategoryChange = (category: API.ComplaintCategory) => {
    if (category === ComplaintCategory.投诉) {
      setSubCategoryOptions([
        { label: '订单投诉', value: ComplaintSubCategory.订单投诉 },
        { label: '人员投诉', value: ComplaintSubCategory.人员投诉 },
        { label: '平台投诉', value: ComplaintSubCategory.平台建议 },
        { label: '服务投诉', value: ComplaintSubCategory.服务建议 },
        { label: '流程投诉', value: ComplaintSubCategory.流程投诉 },
      ]);
    } else {
      setSubCategoryOptions([
        { label: '平台建议', value: ComplaintSubCategory.平台建议 },
        { label: '服务建议', value: ComplaintSubCategory.服务建议 },
        { label: '流程建议', value: ComplaintSubCategory.流程投诉 },
      ]);
    }
    // 清空小类选择
    formRef.current?.setFieldValue('subCategory', undefined);
    formRef.current?.setFieldValue('orderId', undefined);
    formRef.current?.setFieldValue('employeeId', undefined);
  };

  /** 根据小类显示/隐藏相关字段 */
  const handleSubCategoryChange = (subCategory: API.ComplaintSubCategory) => {
    if (subCategory !== ComplaintSubCategory.订单投诉) {
      formRef.current?.setFieldValue('orderId', undefined);
    }
    if (subCategory !== ComplaintSubCategory.人员投诉) {
      formRef.current?.setFieldValue('employeeId', undefined);
    }
  };

  /** 提交表单 */
  const handleSubmit = async (values: any) => {
    try {
      const submitData = {
        ...values,
        createdBy: initialState?.id,
        photoURLs: values.photoURLs || [],
      };

      const response = await complaints.create(submitData);
      if (response.errCode) {
        message.error(response.msg || '录入失败');
        return false;
      }

      message.success('投诉建议录入成功');
      onSuccess();
      return true;
    } catch (error) {
      message.error('录入失败，请重试');
      return false;
    }
  };

  return (
    <ModalForm
      formRef={formRef}
      title="录入投诉建议"
      open={visible}
      onFinish={handleSubmit}
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
        width: 800,
      }}
      layout="horizontal"
      labelCol={{ span: 6 }}
      wrapperCol={{ span: 18 }}
      initialValues={{
        category: ComplaintCategory.投诉,
        orderId,
        customerId,
      }}
    >
      <ProFormRadio.Group
        name="category"
        label="投诉类型"
        options={ComplaintCategoryOptions}
        rules={[{ required: true, message: '请选择投诉类型' }]}
        fieldProps={{
          onChange: (e) => handleCategoryChange(e.target.value),
        }}
      />

      <ProFormSelect
        name="subCategory"
        label="具体分类"
        options={subCategoryOptions}
        rules={[{ required: true, message: '请选择具体分类' }]}
        fieldProps={{
          onChange: handleSubCategoryChange,
        }}
      />

      <ProFormText
        name="title"
        label="标题"
        rules={[
          { required: true, message: '请输入标题' },
          { max: 200, message: '标题不能超过200个字符' },
        ]}
        placeholder="请简要描述问题"
      />

      <ProFormTextArea
        name="content"
        label="详细内容"
        rules={[
          { required: true, message: '请输入详细内容' },
          { max: 2000, message: '内容不能超过2000个字符' },
        ]}
        fieldProps={{
          rows: 4,
          placeholder: '请详细描述投诉建议的具体情况',
        }}
      />

      <ProFormSelect
        name="customerId"
        label="关联客户"
        request={async ({ keyWords }) => {
          const response = await getCustomers({
            keyword: keyWords,
            pageSize: 50,
          });
          if (response.errCode) return [];
          return (response.data?.list || []).map((item) => ({
            label: `${item.nickname} (${item.phone})`,
            value: item.id,
          }));
        }}
        fieldProps={{
          showSearch: true,
          placeholder: '可选，搜索客户姓名或手机号',
        }}
      />

      <ProFormDependency name={['subCategory']}>
        {({ subCategory }) => {
          if (subCategory !== ComplaintSubCategory.订单投诉) {
            return null;
          }
          return (
            <ProFormSelect
              name="orderId"
              label="关联订单"
              request={async ({ keyWords }) => {
                const response = await getOrders({
                  keyword: keyWords,
                  pageSize: 50,
                });
                if (response.errCode) return [];
                return (response.data?.list || []).map((item) => ({
                  label: `${item.sn} - ${
                    item.customer?.nickname || '未知客户'
                  } (${item.serviceTime})`,
                  value: item.id,
                }));
              }}
              fieldProps={{
                showSearch: true,
                placeholder: '订单投诉时必填，搜索订单编号',
              }}
              rules={[
                {
                  required: true,
                  message: '订单投诉时必须选择关联订单',
                },
              ]}
            />
          );
        }}
      </ProFormDependency>

      <ProFormDependency name={['subCategory']}>
        {({ subCategory }) => {
          if (subCategory !== ComplaintSubCategory.人员投诉) {
            return null;
          }
          return (
            <ProFormSelect
              name="employeeId"
              label="关联员工"
              request={async ({ keyWords }) => {
                const response = await getEmployees({
                  keyword: keyWords,
                  pageSize: 50,
                });
                if (response.errCode) return [];
                return (response.data?.list || []).map((item) => ({
                  label: `${item.name} (${item.phone})`,
                  value: item.id,
                }));
              }}
              fieldProps={{
                showSearch: true,
                placeholder: '人员投诉时必填，搜索员工姓名或手机号',
              }}
              rules={[
                {
                  required: true,
                  message: '人员投诉时必须选择关联员工',
                },
              ]}
            />
          );
        }}
      </ProFormDependency>

      <ProFormText
        name="contactInfo"
        label="联系方式"
        fieldProps={{
          placeholder: '可选，客户联系方式',
          maxLength: 100,
        }}
      />

      <ProFormImgMultiple
        name="photoURLs"
        label="相关图片"
        dir="complaint"
        maxCount={6}
        maxSize={{
          size: 2 * 1024 * 1024,
          message: '图片大小不能超过2M',
        }}
        extra="最多上传6张图片，支持jpg、png格式"
      />

      <ProFormTextArea
        name="adminNote"
        label="管理员备注"
        fieldProps={{
          rows: 2,
          placeholder: '可选，管理员录入时的备注信息',
          maxLength: 500,
        }}
      />
    </ModalForm>
  );
};

export default ComplaintCreateModal;

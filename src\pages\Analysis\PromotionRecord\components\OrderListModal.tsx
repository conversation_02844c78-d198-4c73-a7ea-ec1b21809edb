import { customerValidOrders } from '@/services/order';
import { formatCurrency } from '@/utils/format';
import type { ActionType } from '@ant-design/pro-components';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { Modal, Typography } from 'antd';
import moment from 'moment';
import React, { useRef } from 'react';

const { Text } = Typography;

interface OrderListModalProps {
  open: boolean;
  onClose: () => void;
  customerId: number;
  customerName: string;
}

const OrderListModal: React.FC<OrderListModalProps> = ({
  open,
  onClose,
  customerId,
  customerName,
}) => {
  const actionRef = useRef<ActionType>();

  const columns: ProColumns<any>[] = [
    {
      title: '订单号',
      dataIndex: 'sn',
      width: 120,
      copyable: true,
    },
    {
      title: '服务项目',
      dataIndex: 'serviceName',
      width: 120,
      render: (_, record) => {
        const mainServices = record.mainServices || [];
        if (mainServices.length > 0) {
          return mainServices
            .map((service: any) => service.serviceName)
            .join(', ');
        }
        return '-';
      },
    },
    {
      title: '增项',
      dataIndex: 'additionalServiceOrders',
      render: (_, record) => {
        const nameList: string[] = [];
        record.mainServices?.forEach((s: any) => {
          s.additionalServices?.forEach((a: any) => {
            nameList.push(a.name);
          });
        });
        record.additionalServiceOrders?.forEach((a: any) => {
          a.services?.forEach((s: any) => {
            nameList.push(s.serviceName);
          });
        });
        return nameList.join(', ');
      },
    },
    {
      title: '订单金额',
      dataIndex: 'totalOriginalPrice',
      width: 100,
      render: (_, record) => (
        <Text strong style={{ color: '#ff4d4f' }}>
          {formatCurrency(record.totalOriginalPrice)}
        </Text>
      ),
    },
    {
      title: '实付',
      dataIndex: 'totalPaidAmount',
      width: 100,
      render: (_, record) => (
        <Text strong style={{ color: '#ff4d4f' }}>
          {formatCurrency(record.totalPaidAmount)}
        </Text>
      ),
    },
    {
      title: '订单状态',
      dataIndex: 'status',
      width: 100,
      valueEnum: {
        pending_payment: { text: '待付款', status: 'Warning' },
        pending_acceptance: { text: '待接单', status: 'Processing' },
        pending_service: { text: '待服务', status: 'Processing' },
        in_service: { text: '服务中', status: 'Processing' },
        completed: { text: '已完成', status: 'Success' },
        cancelled: { text: '已取消', status: 'Default' },
        refunded: { text: '已退款', status: 'Error' },
      },
    },
    {
      title: '服务时间',
      dataIndex: 'serviceTime',
      width: 140,
      render: (_, record) =>
        record.serviceTime
          ? moment(record.serviceTime).format('YYYY-MM-DD HH:mm')
          : '-',
    },
    {
      title: '创建时间',
      dataIndex: 'orderTime',
      width: 140,
      render: (_, record) =>
        record.orderTime
          ? moment(record.orderTime).format('YYYY-MM-DD HH:mm')
          : '-',
    },
  ];

  return (
    <Modal
      title={`${customerName} - 订单列表`}
      open={open}
      onCancel={onClose}
      footer={null}
      width={1000}
      destroyOnClose
    >
      <ProTable<any>
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        search={false}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
        }}
        options={false}
        request={async (params) => {
          if (!customerId) {
            return {
              data: [],
              success: true,
              total: 0,
            };
          }

          const response = await customerValidOrders({
            customerId,
            current: params.current,
            pageSize: params.pageSize,
            sortBy: 'orderTime',
            sortOrder: 'desc',
          });

          if (response.errCode) {
            return {
              data: [],
              success: false,
              total: 0,
            };
          }

          return {
            data: response.data?.list || [],
            success: true,
            total: response.data?.total || 0,
          };
        }}
      />
    </Modal>
  );
};

export default OrderListModal;

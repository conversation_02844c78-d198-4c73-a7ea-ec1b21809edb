// 定义标记点接口
interface Marker {
  position: [number, number];
  title?: string;
  actived?: boolean; // 是否激活
  status?: number; // 状态码，用于支持不同状态的标记点
  icon?: string | MarkerIcon; // 标记点图标，可以是图片URL或自定义图标对象
  content?: string; // 信息窗体内容
  id?: string | number; // 标记点唯一标识
}

// 定义标记点图标接口
interface MarkerIcon {
  image: string; // 图标图片URL
  size?: [number, number]; // 图标大小 [宽度, 高度]
  offset?: [number, number]; // 图标偏移量 [x, y]
  imageOffset?: [number, number]; // 图片偏移量，用于雪碧图
  imageSize?: [number, number]; // 图片大小，用于雪碧图
}

// 定义地图事件回调接口
interface MapEvents {
  onClick?: (e: any) => void; // 地图点击事件
  onMarkerClick?: (marker: Marker, e: any) => void; // 标记点点击事件
  onDragEnd?: (e: any) => void; // 拖拽结束事件
  onZoomChange?: (zoom: number) => void; // 缩放级别变化事件
}

// 定义地图组件的属性接口
interface MapProps {
  center?: [number, number]; // 中心点坐标 [经度, 纬度]
  city?: string; // 城市名称，如果没有center则通过城市定位
  markers?: Array<Marker>;
  zoom?: number; // 缩放级别
  activedZoom?: number; // 激活标记点时的缩放级别
  enableCluster?: boolean; // 是否启用标记点聚合
  enableInfoWindow?: boolean; // 是否启用信息窗体
  events?: MapEvents; // 地图事件回调
  loadingText?: string; // 加载中文本
  onMapCreated?: (mapInstance: any) => void; // 地图创建完成回调
  customControls?: Array<{
    position: 'LT' | 'RT' | 'LB' | 'RB'; // 控件位置：左上、右上、左下、右下
    content: React.ReactNode; // 控件内容
    offset?: [number, number]; // 偏移量 [x, y]
  }>; // 自定义控件
  lazyLoading?: boolean; // 是否启用懒加载
  timeoutMs?: number; // 超时时间（毫秒）
  markerRender?: (marker: Marker) => string; // 自定义标记点渲染函数
  style?: React.CSSProperties; // 地图容器样式
  className?: string; // 地图容器类名
  wrapperStyle?: React.CSSProperties; // 地图外层容器样式
  wrapperClassName?: string; // 地图外层容器类名
}

/** 高德地图地理编码返回结果类型 */
type AmapGeocodeResult = {
  /** 经纬度坐标 [经度, 纬度] */
  location: {
    /** 类名标识 */
    className: 'AMap.LngLat';
    /** 经度值 */
    KL: number;
    /** 纬度值 */
    kT: number;
    /** 纬度 */
    lat: number;
    /** 经度 */
    lng: number;
  };
  /** 格式化后的地址 */
  formattedAddress: string;
  /** 区域编码 */
  adcode: string;
  /** 行政区划级别 */
  level: string;
  /** 地址组成部分 */
  addressComponent: {
    /** 社区名称 */
    neighborhood: string;
    /** 社区类型 */
    neighborhoodType: string;
    /** 建筑名称 */
    building: string;
    /** 建筑类型 */
    buildingType: string;
    /** 省份名称 */
    province: string;
    /** 城市名称 */
    city: string;
    /** 区县名称 */
    district: string;
    /** 乡镇名称 */
    township: string;
    /** 城市编码 */
    citycode: string;
    /** 街道名称 */
    street: string;
    /** 门牌号 */
    streetNumber: string;
  };
};

import { ReloadOutlined } from '@ant-design/icons';
import { Page<PERSON>ontainer, ProCard } from '@ant-design/pro-components';
import { Button, Col, DatePicker, Row, Tabs } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import React, { useState } from 'react';
import CustomerStats from './components/CustomerStats';
import EmployeeStats from './components/EmployeeStats';
import StatusDistribution from './components/StatusDistribution';
import TrendChart from './components/TrendChart';

const { RangePicker } = DatePicker;

const ComplaintAnalysis: React.FC = () => {
  // 日期范围状态
  const [dateRange, setDateRange] = useState<[Dayjs, Dayjs]>([
    dayjs().subtract(6, 'month'),
    dayjs(),
  ]);

  // 刷新数据 - 通过重新设置日期范围来触发子组件刷新
  const handleRefresh = () => {
    setDateRange([...dateRange]);
  };

  // Tab页签配置
  const tabItems = [
    {
      key: 'overview',
      label: '数据概览',
      children: (
        <div>
          {/* 第一行：三个饼图并列显示 */}
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={8}>
              <StatusDistribution
                dateRange={dateRange}
                viewType="status"
                title="状态分布"
                showSelector={false}
              />
            </Col>
            <Col xs={24} sm={8}>
              <StatusDistribution
                dateRange={dateRange}
                viewType="category"
                title="类型分布"
                showSelector={false}
              />
            </Col>
            <Col xs={24} sm={8}>
              <StatusDistribution
                dateRange={dateRange}
                viewType="subCategory"
                title="子类分布"
                showSelector={false}
              />
            </Col>
          </Row>

          {/* 第二行：趋势图 */}
          <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
            <Col span={24}>
              <TrendChart dateRange={dateRange} />
            </Col>
          </Row>
        </div>
      ),
    },
    {
      key: 'customer',
      label: '客户统计',
      children: <CustomerStats dateRange={dateRange} />,
    },
    {
      key: 'employee',
      label: '员工统计',
      children: <EmployeeStats dateRange={dateRange} />,
    },
  ];

  return (
    <PageContainer
      title="投诉建议数据分析"
      subTitle="投诉建议统计与业务分析"
      breadcrumb={{ items: [] }}
      extra={[
        <RangePicker
          key="dateRange"
          value={dateRange}
          onChange={(dates) => {
            if (dates) {
              setDateRange(dates as [Dayjs, Dayjs]);
            }
          }}
          style={{ marginRight: 16 }}
        />,
        <Button
          key="refresh"
          type="primary"
          icon={<ReloadOutlined />}
          onClick={handleRefresh}
        >
          刷新数据
        </Button>,
      ]}
    >
      <ProCard>
        <Tabs items={tabItems} />
      </ProCard>
    </PageContainer>
  );
};

export default ComplaintAnalysis;

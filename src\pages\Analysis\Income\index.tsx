import * as revenueStatistics from '@/services/revenue-statistics';
import { PageContainer } from '@ant-design/pro-components';
import { Card, Col, DatePicker, Row, Tabs, message } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import React, { useEffect, useState } from 'react';
import CompositionChart from './components/CompositionChart';
import EmployeeStats from './components/EmployeeStats';
import ServiceStats from './components/ServiceStats';
import StatisticsCards from './components/StatisticsCards';
import TrendChart from './components/TrendChart';

const { RangePicker } = DatePicker;

const IncomeAnalysis: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [overviewData, setOverviewData] = useState<API.RevenueOverviewStats>();

  // 日期范围状态，默认近半年
  const [dateRange, setDateRange] = useState<[Dayjs, Dayjs]>([
    dayjs().subtract(6, 'month'),
    dayjs(),
  ]);

  // 获取概览数据（不传时间参数）
  const fetchOverviewData = async () => {
    try {
      setLoading(true);
      const { errCode, msg, data } = await revenueStatistics.overview();

      if (errCode) {
        message.error(msg || '获取收入概览数据失败');
        return;
      }

      if (data) {
        setOverviewData(data);
      }
    } catch (error) {
      console.error('获取收入概览数据失败:', error);
      message.error('获取收入概览数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOverviewData();
  }, []);

  // 处理日期范围变化
  const handleDateRangeChange = (dates: any) => {
    if (dates) {
      setDateRange(dates as [Dayjs, Dayjs]);
    }
  };

  // Tab页签配置
  const tabItems = [
    {
      key: 'overview',
      label: '数据概览',
      children: (
        <div>
          {/* 统计卡片区域 */}
          <StatisticsCards data={overviewData} loading={loading} />

          <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
            <Col xs={24}>
              <CompositionChart dateRange={dateRange} />
            </Col>
          </Row>

          {/* 图表分析区域 */}
          <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
            <Col xs={24}>
              <TrendChart dateRange={dateRange} />
            </Col>
          </Row>
        </div>
      ),
    },
    {
      key: 'service',
      label: '服务统计',
      children: <ServiceStats dateRange={dateRange} />,
    },
    {
      key: 'employee',
      label: '员工统计',
      children: <EmployeeStats dateRange={dateRange} />,
    },
  ];

  return (
    <PageContainer
      title="收入数据分析"
      subTitle="收入统计与分析"
      breadcrumb={{ items: [] }}
      extra={[
        <RangePicker
          key="dateRange"
          value={dateRange}
          onChange={handleDateRangeChange}
          allowClear={false}
          format="YYYY-MM-DD"
        />,
      ]}
    >
      <Card>
        <Tabs items={tabItems} />
      </Card>
    </PageContainer>
  );
};

export default IncomeAnalysis;

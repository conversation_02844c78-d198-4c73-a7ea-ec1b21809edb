import { employeePromotion } from '@/services';
import { getCustomer } from '@/services/customers';
import { EyeOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, message, Space, Tag, Tooltip } from 'antd';
import moment from 'moment';
import React, { useRef, useState } from 'react';
import UserDetailDrawer from './UserDetailDrawer';

interface EmployeePromotionTableProps {
  onRefresh?: () => void;
}

const EmployeePromotionTable: React.FC<EmployeePromotionTableProps> = ({}) => {
  const actionRef = useRef<ActionType>();

  // 用户详情相关状态
  const [userDetailVisible, setUserDetailVisible] = useState(false);
  const [currentUser, setCurrentUser] = useState<API.Customer | null>(null);
  const [userDetailTitle, setUserDetailTitle] = useState('');

  // 查看用户详情
  const handleViewUserDetail = async (userId: number, title: string) => {
    setUserDetailTitle(title);
    setUserDetailVisible(true);

    try {
      const { errCode, msg, data } = await getCustomer(userId);

      if (errCode) {
        message.error(msg || '获取用户详情失败');
        setCurrentUser(null);
        return;
      }

      setCurrentUser(data || null);
    } catch (error) {
      console.error('获取用户详情失败:', error);
      message.error('获取用户详情失败');
      setCurrentUser(null);
    }
  };

  // 关闭用户详情
  const handleCloseUserDetail = () => {
    setUserDetailVisible(false);
    setCurrentUser(null);
    setUserDetailTitle('');
  };

  const columns: ProColumns<API.EmployeePromotion, 'text'>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      hideInTable: true,
      hideInSearch: true,
    },
    {
      title: '用户昵称',
      dataIndex: 'nickname',
      key: 'nickname',
      width: 120,
      render: (_, record) => (
        <Space>
          <span>{record.nickname || '未知用户'}</span>
          <Tooltip title="查看用户详情">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => {
                handleViewUserDetail(record.id, '用户详情');
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
    {
      title: '用户手机号',
      dataIndex: 'phone',
      key: 'phone',
      width: 120,
    },
    {
      title: '会员状态',
      dataIndex: 'memberStatus',
      key: 'memberStatus',
      width: 100,
      valueType: 'select',
      valueEnum: {
        0: { text: '非会员', status: 'Default' },
        1: { text: '会员', status: 'Success' },
      },
      render: (_, record) => (
        <Tag color={record.memberStatus === 1 ? 'green' : 'default'}>
          {record.memberStatus === 1 ? '会员' : '非会员'}
        </Tag>
      ),
    },
    {
      title: '推广员工',
      dataIndex: ['promotionEmployee', 'name'],
      key: 'promotionEmployeeName',
      width: 120,
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <span>{record.promotionEmployee?.name || '未知员工'}</span>
          <span style={{ fontSize: '12px', color: '#666' }}>
            {record.promotionEmployee?.phone}
          </span>
        </Space>
      ),
    },
    {
      title: '推广码',
      dataIndex: ['promotionEmployee', 'promotionCode'],
      key: 'promotionCode',
      width: 120,
      copyable: true,
      hideInSearch: true,
    },
    {
      title: '员工ID',
      dataIndex: 'promotionEmployeeId',
      key: 'employeeId',
      width: 100,
      hideInTable: true,
    },
    {
      title: '关键词',
      dataIndex: 'keyword',
      key: 'keyword',
      hideInTable: true,
      tooltip: '搜索员工姓名、手机号、推广码',
    },
    {
      title: '建立时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      valueType: 'dateRange',
      width: 160,
      sorter: true,
      render: (_, record) => {
        return record.updatedAt
          ? moment(record.updatedAt).format('YYYY-MM-DD HH:mm:ss')
          : '-';
      },
      search: {
        transform: (value) => {
          return {
            startTime: value[0],
            endTime: value[1],
          };
        },
      },
    },
  ];

  return (
    <>
      <ProTable<API.EmployeePromotion>
        actionRef={actionRef}
        rowKey="id"
        headerTitle="员工推广关系"
        columns={columns}
        search={{
          labelWidth: 'auto',
        }}
        pagination={{
          pageSize: 20,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
        }}
        scroll={{ x: 1200 }}
        request={async (params, sort, filter) => {
          const { errCode, msg, data } = await employeePromotion.index({
            ...params,
            ...sort,
            filter,
          });
          if (errCode) {
            message.error(msg || '列表查询失败');
            return {
              data: [],
              total: 0,
              success: false,
            };
          }
          return {
            data: data?.list || [],
            total: data?.total || 0,
            success: true,
          };
        }}
        options={{
          reload: true,
          density: true,
          fullScreen: true,
          setting: true,
        }}
      />

      {/* 用户详情抽屉 */}
      <UserDetailDrawer
        open={userDetailVisible}
        onClose={handleCloseUserDetail}
        user={currentUser}
        title={userDetailTitle}
      />
    </>
  );
};

export default EmployeePromotionTable;

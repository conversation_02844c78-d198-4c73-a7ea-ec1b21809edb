import * as complaints from '@/services/complaints';
import { WordCloud } from '@ant-design/charts';
import { ProCard } from '@ant-design/pro-components';
import { Card, Col, List, message, Row, Tag } from 'antd';
import { Dayjs } from 'dayjs';
import React, { useEffect, useState } from 'react';

interface HotIssuesAnalysisProps {
  dateRange: [Dayjs, Dayjs];
}

const HotIssuesAnalysis: React.FC<HotIssuesAnalysisProps> = ({ dateRange }) => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<API.ComplaintHotIssuesStats>({
    hotKeywords: [],
    subCategoryHotIssues: [],
  });

  const fetchData = async () => {
    setLoading(true);
    try {
      const {
        errCode,
        msg,
        data: responseData,
      } = await complaints.hotIssues({
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD'),
        limit: 20,
      });

      if (errCode) {
        message.error(msg || '获取热点问题数据失败');
        return;
      }

      if (responseData) {
        setData(responseData);
      }
    } catch (error) {
      console.error('获取热点问题数据失败:', error);
      message.error('获取热点问题数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [dateRange]);

  // 转换关键词数据格式用于词云图
  const wordCloudData = data.hotKeywords.map((item) => ({
    text: item.keyword,
    value: item.count,
  }));

  // 子分类标签映射
  const getSubCategoryLabel = (subCategory: string) => {
    const subCategoryMap: Record<string, string> = {
      order: '订单投诉',
      employee: '人员投诉',
      platform: '平台建议',
      service: '服务建议',
    };
    return subCategoryMap[subCategory] || subCategory;
  };

  // 获取标签颜色
  const getTagColor = (subCategory: string) => {
    const colorMap: Record<string, string> = {
      order: 'red',
      employee: 'orange',
      platform: 'blue',
      service: 'green',
    };
    return colorMap[subCategory] || 'default';
  };

  const wordCloudConfig = {
    data: wordCloudData,
    wordField: 'text',
    weightField: 'value',
    colorField: 'text',
    wordStyle: {
      fontFamily: 'Verdana',
      fontSize: [8, 32] as [number, number],
      rotation: 0,
    },
    interactions: [
      {
        type: 'element-active',
      },
    ],
    state: {
      active: {
        style: {
          lineWidth: 1,
        },
      },
    },
  };

  return (
    <ProCard title="热点问题分析" loading={loading}>
      <Row gutter={[16, 16]}>
        {/* 热门关键词词云图 */}
        <Col xs={24} lg={14}>
          <Card title="热门关键词" size="small" style={{ height: '100%' }}>
            {wordCloudData.length > 0 ? (
              <WordCloud {...wordCloudConfig} height={200} />
            ) : (
              <div
                style={{
                  textAlign: 'center',
                  padding: '50px 0',
                  color: '#999',
                }}
              >
                暂无关键词数据
              </div>
            )}
          </Card>
        </Col>

        {/* 热点问题类型排行 */}
        <Col xs={24} lg={10}>
          <Card title="热点问题类型" size="small" style={{ height: '100%' }}>
            <List
              size="small"
              dataSource={data.subCategoryHotIssues}
              renderItem={(item, index) => (
                <List.Item>
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      width: '100%',
                    }}
                  >
                    <div>
                      <span style={{ marginRight: 8, color: '#999' }}>
                        #{index + 1}
                      </span>
                      <Tag color={getTagColor(item.subCategory)}>
                        {getSubCategoryLabel(item.subCategory)}
                      </Tag>
                    </div>
                    <span style={{ fontWeight: 'bold', color: '#1890ff' }}>
                      {item.count} 件
                    </span>
                  </div>
                </List.Item>
              )}
              locale={{ emptyText: '暂无数据' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 关键词排行榜 */}
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col xs={24}>
          <Card title="关键词排行榜" size="small">
            <List
              grid={{ gutter: 16, xs: 1, sm: 2, md: 3, lg: 4, xl: 6, xxl: 8 }}
              dataSource={data.hotKeywords.slice(0, 16)}
              renderItem={(item, index) => (
                <List.Item>
                  <Card size="small" style={{ textAlign: 'center' }}>
                    <div
                      style={{
                        fontSize: '12px',
                        color: '#999',
                        marginBottom: 4,
                      }}
                    >
                      #{index + 1}
                    </div>
                    <div style={{ fontWeight: 'bold', marginBottom: 4 }}>
                      {item.keyword}
                    </div>
                    <div style={{ color: '#1890ff', fontSize: '14px' }}>
                      {item.count} 次
                    </div>
                  </Card>
                </List.Item>
              )}
              locale={{ emptyText: '暂无数据' }}
            />
          </Card>
        </Col>
      </Row>
    </ProCard>
  );
};

export default HotIssuesAnalysis;

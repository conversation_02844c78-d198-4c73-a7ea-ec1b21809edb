import { employeeActionLogs, employees } from '@/services';
import { BarChartOutlined, UserOutlined } from '@ant-design/icons';
import {
  ProFormDateRangePicker,
  ProFormSelect,
} from '@ant-design/pro-components';
import { Card, Col, Row, Statistic, Table, message } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';

const ActionOverview: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [overview, setOverview] = useState<API.EmployeeActionOverview>();
  const [filters, setFilters] = useState<{
    startDate?: string;
    endDate?: string;
    employeeId?: number;
  }>({
    startDate: dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
    endDate: dayjs().format('YYYY-MM-DD'),
  });

  const fetchOverview = async () => {
    setLoading(true);
    try {
      const { errCode, msg, data } = await employeeActionLogs.overview(filters);
      if (errCode) {
        message.error(msg || '获取统计概览失败');
      } else {
        setOverview(data);
      }
    } catch (error) {
      console.error('获取统计概览失败:', error);
      message.error('获取统计概览失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOverview();
  }, [filters]);

  const handleFiltersChange = (values: any) => {
    const newFilters: any = { ...filters };

    if (values.dateRange) {
      newFilters.startDate = values.dateRange[0]?.format('YYYY-MM-DD');
      newFilters.endDate = values.dateRange[1]?.format('YYYY-MM-DD');
    }

    if (values.employeeId !== undefined) {
      newFilters.employeeId = values.employeeId;
    }

    setFilters(newFilters);
  };

  const columns = [
    {
      title: '动作类型',
      dataIndex: 'changeTypeLabel',
      key: 'changeTypeLabel',
    },
    {
      title: '执行次数',
      dataIndex: 'count',
      key: 'count',
      render: (count: number) => (
        <Statistic value={count} valueStyle={{ fontSize: 16 }} />
      ),
    },
    {
      title: '占比',
      key: 'percentage',
      render: (_: any, record: any) => {
        const percentage = overview?.totalActions
          ? ((record.count / overview.totalActions) * 100).toFixed(1)
          : '0.0';
        return `${percentage}%`;
      },
    },
  ];

  return (
    <div>
      {/* 筛选条件 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={8}>
            <ProFormDateRangePicker
              name="dateRange"
              label="时间范围"
              initialValue={[dayjs(filters.startDate), dayjs(filters.endDate)]}
              fieldProps={{
                onChange: (dates) => {
                  handleFiltersChange({
                    dateRange: dates,
                  });
                },
              }}
            />
          </Col>
          <Col span={8}>
            <ProFormSelect
              name="employeeId"
              label="员工筛选"
              placeholder="请选择员工（可选）"
              request={async () => {
                const { errCode, data } = await employees.index({});
                if (errCode || !data?.list) return [];
                return data.list.map((emp) => ({
                  label: `${emp.name} (${emp.phone})`,
                  value: emp.id,
                }));
              }}
              fieldProps={{
                allowClear: true,
                onChange: (value) => {
                  handleFiltersChange({
                    employeeId: value,
                  });
                },
              }}
            />
          </Col>
        </Row>
      </Card>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={12}>
          <Card>
            <Statistic
              title="总动作数"
              value={overview?.totalActions || 0}
              prefix={<BarChartOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={12}>
          <Card>
            <Statistic
              title="动作类型数"
              value={overview?.actionStats?.length || 0}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 动作统计表格 */}
      <Card title="动作类型统计" loading={loading}>
        <Table
          columns={columns}
          dataSource={overview?.actionStats || []}
          rowKey="changeType"
          pagination={false}
          size="middle"
        />
      </Card>
    </div>
  );
};

export default ActionOverview;

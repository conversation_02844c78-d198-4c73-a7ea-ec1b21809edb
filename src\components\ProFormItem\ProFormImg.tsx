/*
 * @Description: 图片上传组件，在ProForm中使用
 * @Date: 2025-02-13 10:11:28
 * @LastEditors: ZhuPengliang <EMAIL>
 * @LastEditTime: 2025-06-29 03:05:44
 */
import { deleteFile as delete_cos, upload as upload_cos } from '@/utils/cos';
import { deleteFile as delete_zos, upload as upload_zos } from '@/utils/zos';
import {
  ProFormUploadButton,
  ProFormUploadButtonProps,
} from '@ant-design/pro-components';
import { message, Modal, Upload, UploadProps } from 'antd';
import React from 'react';

type ProFormImgProps = ProFormUploadButtonProps & {
  /** 大小限制，默认1M */
  maxSize?: {
    size: number;
    message: string;
  };
  /** 上传路径前缀，默认avatar */
  dir?: string;
};

const ProFormImg: React.FC<ProFormImgProps> = ({
  maxSize = {
    size: 1 * 1024 * 1024,
    message: '图片大小不能超过1M',
  },
  dir = 'avatar',
  ...uploadProps
}) => {
  const customRequest: UploadProps['customRequest'] = async (options) => {
    if (!options.file) {
      message.warning('没有读取到文件，请重新上传！');
      return;
    }

    const file = options.file as File;
    /** 云对象存储上传 */
    const upload = uploadType === 'cos' ? upload_cos : upload_zos;
    await upload({
      file,
      dir,
      name: `${Date.now()}_${file.name}`,
      onProgress: (progressData) => {
        options.onProgress?.({ percent: progressData.percent * 100 });
      },
      onOK: (url) => {
        options.onSuccess?.(url);
      },
      onError: (err) => {
        options.onError?.(err);
      },
    });
  };

  return (
    <ProFormUploadButton
      {...uploadProps}
      // name="avatar"
      // label="头像"
      max={1}
      listType="picture-card"
      fieldProps={{
        multiple: false,
        accept: 'image/*',
        onPreview: (file) => {
          const url = file.thumbUrl || file.url || file.response;
          if (url) {
            Modal.info({
              title: '预览',
              content: (
                <img
                  src={url}
                  alt="预览"
                  style={{ maxWidth: '100%', maxHeight: '100%' }}
                />
              ),
            });
          }
        },
        beforeUpload: (file) => {
          const isImage = file.type.startsWith('image/');
          if (!isImage) {
            message.error(`${file.name} 不是一个图片文件`);
            return Upload.LIST_IGNORE;
          }
          if (file.size > maxSize.size) {
            message.error(maxSize.message);
            return Upload.LIST_IGNORE;
          }
          return true;
        },
        customRequest,
        onRemove: (file) => {
          if (file.status === 'done') {
            // const key = file.response?.split('myqcloud.com/')[1];
            const deleteFile = uploadType === 'cos' ? delete_cos : delete_zos;
            deleteFile({ url: file.response as string });
          }
          return true;
        },
      }}
      // initialValue={initialValue}
      transform={(value) => {
        if (Array.isArray(value) && value.length > 0) {
          return '//' + value[0].response;
        }
        if (typeof value === 'string') {
          return value;
        }
        return '';
      }}
      convertValue={(value) => {
        if (Array.isArray(value)) {
          return value;
        }
        return value
          ? [
              {
                response: value,
                thumbUrl: value,
                status: 'done',
                type: 'image/jpeg',
              },
            ]
          : [];
      }}
    />
  );
};

export default ProFormImg;

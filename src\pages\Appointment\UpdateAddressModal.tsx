import AddressSelector from '@/components/AddressSelector';
import AddressResolver from '@/components/GaoDeMap/AddressResolver';
import { updateServiceAddress } from '@/services/order';
import { Button, Descriptions, Modal, Space, Typography, message } from 'antd';
import React, { useState } from 'react';

const { Text } = Typography;

interface UpdateAddressModalProps {
  visible: boolean;
  order?: API.Order;
  onClose: () => void;
  onSuccess: (updatedOrderData?: Partial<API.Order>) => void;
}

/**
 * 订单地址修改模态框
 */
const UpdateAddressModal: React.FC<UpdateAddressModalProps> = ({
  visible,
  order,
  onClose,
  onSuccess,
}) => {
  const [addressSelectorVisible, setAddressSelectorVisible] = useState(false);
  const [selectedAddress, setSelectedAddress] = useState<{
    addressId?: number;
    address: string;
    addressDetail?: string;
    longitude: number;
    latitude: number;
    addressRemark?: string;
  }>();
  const [loading, setLoading] = useState(false);

  // 处理地址选择
  const handleAddressSelect = (addressData: {
    addressId?: number;
    address: string;
    addressDetail?: string;
    longitude: number;
    latitude: number;
    addressRemark?: string;
  }) => {
    // 确保地址和详细地址字段正确映射
    const processedAddress = {
      ...addressData,
      // 如果选择的是用户已保存地址，需要正确分离主地址和详细地址
      address: addressData.address,
      addressDetail: addressData.addressDetail || '',
    };

    setSelectedAddress(processedAddress);
    setAddressSelectorVisible(false);
  };

  // 处理关闭
  const handleClose = () => {
    setSelectedAddress(undefined);
    onClose();
  };

  // 处理确认修改
  const handleConfirm = async () => {
    if (!order || !selectedAddress) {
      message.warning('请先选择新的服务地址');
      return;
    }

    setLoading(true);
    try {
      const response = await updateServiceAddress(order.id, {
        address: selectedAddress.address,
        addressDetail: selectedAddress.addressDetail || '',
        longitude: selectedAddress.longitude,
        latitude: selectedAddress.latitude,
        addressRemark: selectedAddress.addressRemark || '',
        addressId: selectedAddress.addressId || null,
        userType: 'admin', // 管理端操作
      });

      if (response.errCode) {
        message.error(response.msg || '修改地址失败');
      } else {
        message.success('地址修改成功');

        // 构建更新后的订单数据
        const updatedOrderData: Partial<API.Order> = {
          address: selectedAddress.address,
          addressDetail: selectedAddress.addressDetail || '',
          longitude: selectedAddress.longitude,
          latitude: selectedAddress.latitude,
          addressRemark: selectedAddress.addressRemark || '',
          addressId: selectedAddress.addressId || undefined,
        };

        // 先关闭模态框，再触发刷新，确保刷新时模态框已关闭
        handleClose();
        // 延迟一下确保状态更新完成
        setTimeout(() => {
          onSuccess(updatedOrderData);
        }, 100);
      }
    } catch (error) {
      console.error('修改地址失败:', error);
      message.error('修改地址失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  if (!order) return null;

  return (
    <>
      <Modal
        title="修改订单服务地址"
        open={visible}
        onCancel={handleClose}
        width={700}
        footer={[
          <Button key="cancel" onClick={handleClose}>
            取消
          </Button>,
          <Button
            key="confirm"
            type="primary"
            onClick={handleConfirm}
            disabled={!selectedAddress}
            loading={loading}
          >
            确认修改
          </Button>,
        ]}
      >
        <Space direction="vertical" style={{ width: '100%' }} size="large">
          {/* 订单基本信息 */}
          <Descriptions title="订单信息" column={2} size="small" bordered>
            <Descriptions.Item label="订单编号">{order.sn}</Descriptions.Item>
            <Descriptions.Item label="订单状态">
              {order.status}
            </Descriptions.Item>
            <Descriptions.Item label="客户信息">
              {order.customer?.nickname || `用户${order.customerId}`}
              {order.customer?.phone && ` (${order.customer.phone})`}
            </Descriptions.Item>
            <Descriptions.Item label="服务时间">
              {order.serviceTime
                ? new Date(order.serviceTime).toLocaleString()
                : '未设置'}
            </Descriptions.Item>
          </Descriptions>

          {/* 当前地址信息 */}
          <div>
            <Text strong style={{ marginBottom: 8, display: 'block' }}>
              当前服务地址：
            </Text>
            <div
              style={{
                padding: '12px',
                backgroundColor: '#fafafa',
                borderRadius: '6px',
              }}
            >
              <Space
                direction="vertical"
                size="small"
                style={{ width: '100%' }}
              >
                <div>
                  <Text strong>地址：</Text>
                  <AddressResolver
                    longitude={order.longitude}
                    latitude={order.latitude}
                    originalAddress={order.address}
                    showCoordinates={false}
                    style={{ display: 'inline-block' }}
                  />
                </div>
                {order.addressDetail && (
                  <div>
                    <Text strong>详细地址：</Text>
                    <Text>{order.addressDetail}</Text>
                  </div>
                )}
                {order.addressRemark && (
                  <div>
                    <Text strong>地址备注：</Text>
                    <Text>{order.addressRemark}</Text>
                  </div>
                )}
                <div>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    经纬度: {order.longitude}, {order.latitude}
                  </Text>
                </div>
              </Space>
            </div>
          </div>

          {/* 新地址选择 */}
          <div>
            <Text strong style={{ marginBottom: 8, display: 'block' }}>
              选择新的服务地址：
            </Text>
            {selectedAddress ? (
              <div
                style={{
                  padding: '12px',
                  backgroundColor: '#f6ffed',
                  borderRadius: '6px',
                  border: '1px solid #52c41a',
                }}
              >
                <Space
                  direction="vertical"
                  size="small"
                  style={{ width: '100%' }}
                >
                  <div>
                    <Text strong>地址：</Text>
                    <Text>{selectedAddress.address}</Text>
                  </div>
                  {selectedAddress.addressDetail && (
                    <div>
                      <Text strong>详细地址：</Text>
                      <Text>{selectedAddress.addressDetail}</Text>
                    </div>
                  )}
                  {selectedAddress.addressRemark && (
                    <div>
                      <Text strong>地址备注：</Text>
                      <Text>{selectedAddress.addressRemark}</Text>
                    </div>
                  )}
                  <div>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      经纬度: {selectedAddress.longitude.toFixed(6)},
                      {selectedAddress.latitude.toFixed(6)}
                    </Text>
                  </div>
                  <Button
                    size="small"
                    onClick={() => setAddressSelectorVisible(true)}
                  >
                    重新选择
                  </Button>
                </Space>
              </div>
            ) : (
              <Button
                type="dashed"
                onClick={() => setAddressSelectorVisible(true)}
                style={{ width: '100%', height: '60px' }}
              >
                点击选择新的服务地址
              </Button>
            )}
          </div>

          {/* 权限说明 */}
          <div
            style={{
              padding: '8px 12px',
              backgroundColor: '#e6f7ff',
              borderRadius: '6px',
            }}
          >
            <Text type="secondary" style={{ fontSize: '12px' }}>
              管理端可以修改任何状态订单的服务地址以支持各种售后服务
            </Text>
          </div>
        </Space>
      </Modal>

      {/* 地址选择器 */}
      <AddressSelector
        visible={addressSelectorVisible}
        customerId={order.customerId}
        onClose={() => setAddressSelectorVisible(false)}
        onSelect={handleAddressSelect}
      />
    </>
  );
};

export default UpdateAddressModal;

import ProFormImg from '@/components/ProFormItem/ProFormImg';
import { create, update } from '@/services/photo-walls';
import {
  ModalForm,
  ProFormDigit,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Col, message, Row } from 'antd';
import React from 'react';

interface EditModalProps {
  visible: boolean;
  current?: API.PhotoWall;
  onClose: () => void;
  onSave: (values: any) => Promise<void>;
}

const EditModal: React.FC<EditModalProps> = ({
  visible,
  current,
  onClose,
  onSave,
}) => {
  const handleFinish = async (values: any) => {
    try {
      if (current?.id) {
        // 编辑模式
        const { errCode, msg } = await update(current.id, values);
        if (errCode) {
          message.error(msg || '更新失败');
          return false;
        }
      } else {
        // 新增模式
        const { errCode, msg } = await create(values);
        if (errCode) {
          message.error(msg || '创建失败');
          return false;
        }
      }

      await onSave(values);
      return true;
    } catch (error) {
      message.error('操作失败');
      return false;
    }
  };

  return (
    <ModalForm
      title={current?.id ? '编辑照片墙' : '新增照片墙'}
      open={visible}
      onOpenChange={(open) => {
        if (!open) {
          onClose();
        }
      }}
      onFinish={handleFinish}
      initialValues={current}
      width={800}
      layout="horizontal"
      labelCol={{ span: 6 }}
      modalProps={{
        destroyOnClose: true,
      }}
    >
      <Row gutter={16}>
        <Col span={12}>
          <ProFormText
            name="title"
            label="照片标题"
            rules={[{ required: true, message: '请输入照片标题' }]}
            placeholder="请输入照片标题"
          />
        </Col>
        <Col span={12}>
          <ProFormText
            name="serviceTypeName"
            label="服务类型"
            placeholder="请输入服务类型名称"
          />
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <ProFormText
            name="petName"
            label="宠物名称"
            placeholder="请输入宠物名称"
          />
        </Col>
        <Col span={12}>
          <ProFormSelect
            name="petType"
            label="宠物类型"
            placeholder="请选择宠物类型"
            options={[
              { label: '狗', value: '狗' },
              { label: '猫', value: '猫' },
              { label: '其他', value: '其他' },
            ]}
          />
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <ProFormImg
            name="beforePhoto"
            label="服务前照片"
            rules={[{ required: true, message: '请上传服务前照片' }]}
            dir="photo-wall"
          />
        </Col>
        <Col span={12}>
          <ProFormImg
            name="afterPhoto"
            label="服务后照片"
            rules={[{ required: true, message: '请上传服务后照片' }]}
            dir="photo-wall"
          />
        </Col>
      </Row>

      <ProFormTextArea
        name="description"
        label="照片描述"
        placeholder="请输入照片描述"
        fieldProps={{
          rows: 3,
        }}
      />

      <Row gutter={16}>
        <Col span={12}>
          <ProFormDigit
            name="priority"
            label="展示优先级"
            placeholder="请输入优先级"
            min={0}
            max={999}
            initialValue={10}
            fieldProps={{
              precision: 0,
            }}
            extra="数值越大优先级越高"
          />
        </Col>
      </Row>
    </ModalForm>
  );
};

export default EditModal;

import * as complaints from '@/services/complaints';
import { Column } from '@ant-design/charts';
import { ProCard } from '@ant-design/pro-components';
import { message, Select } from 'antd';
import { Dayjs } from 'dayjs';
import React, { useEffect, useState } from 'react';

interface TimeDistributionProps {
  dateRange: [Dayjs, Dayjs];
}

const TimeDistribution: React.FC<TimeDistributionProps> = ({ dateRange }) => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<API.ComplaintTimeDistributionStats[]>([]);
  const [timeType, setTimeType] = useState<'hour' | 'weekday' | 'month'>(
    'hour',
  );

  const fetchData = async () => {
    setLoading(true);
    try {
      const {
        errCode,
        msg,
        data: responseData,
      } = await complaints.timeDistribution({
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD'),
        timeType,
      });

      if (errCode) {
        message.error(msg || '获取时间分布数据失败');
        return;
      }

      if (responseData) {
        setData(responseData);
      }
    } catch (error) {
      console.error('获取时间分布数据失败:', error);
      message.error('获取时间分布数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [dateRange, timeType]);

  // 转换数据格式用于图表
  const chartData = data.flatMap((item) => [
    {
      period: item.label,
      type: '总数量',
      value: item.count,
    },
    {
      period: item.label,
      type: '投诉数量',
      value: item.complaintCount,
    },
    {
      period: item.label,
      type: '建议数量',
      value: item.suggestionCount,
    },
  ]);

  function getTimeTypeLabel() {
    switch (timeType) {
      case 'hour':
        return '时间段';
      case 'weekday':
        return '星期';
      case 'month':
        return '月份';
      default:
        return '时间';
    }
  }

  const config = {
    data: chartData,
    xField: 'period',
    yField: 'value',
    seriesField: 'type',
    isGroup: true,
    columnStyle: {
      radius: [2, 2, 0, 0],
    },
    color: ['#1890ff', '#ff4d4f', '#52c41a'],
    legend: {
      position: 'top' as const,
    },
    label: {
      position: 'middle' as const,
      style: {
        fill: '#FFFFFF',
        opacity: 0.6,
      },
    },
    xAxis: {
      label: {
        autoHide: true,
        autoRotate: false,
      },
    },
    meta: {
      period: {
        alias: getTimeTypeLabel(),
      },
      value: {
        alias: '数量',
      },
    },
    tooltip: {
      shared: true,
      showCrosshairs: true,
    },
  };

  return (
    <ProCard
      title="时间分布统计"
      loading={loading}
      extra={
        <Select
          value={timeType}
          onChange={setTimeType}
          style={{ width: 120 }}
          options={[
            { label: '按小时', value: 'hour' },
            { label: '按星期', value: 'weekday' },
            { label: '按月份', value: 'month' },
          ]}
        />
      }
    >
      <Column {...config} height={300} />
    </ProCard>
  );
};

export default TimeDistribution;

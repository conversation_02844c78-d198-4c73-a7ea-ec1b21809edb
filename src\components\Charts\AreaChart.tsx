import { Area } from '@ant-design/plots';
import { Empty } from 'antd';
import React from 'react';

export interface AreaChartData {
  period: string;
  value: number;
  type?: string; // 可选，用于多系列数据
}

interface AreaChartProps {
  data: AreaChartData[];
  height?: number;
  smooth?: boolean;
  emptyText?: string;
  xAxisLabel?: string;
  yAxisLabel?: string;
  valueUnit?: string;
  // 区域图特有配置
  isStack?: boolean; // 是否堆积
  color?: string | string[]; // 单色或多色配置
  areaStyle?: {
    fill?: string;
    fillOpacity?: number;
  };
  // 时间轴配置
  isTimeAxis?: boolean;
  dateFormat?: string;
}

const AreaChart: React.FC<AreaChartProps> = ({
  data,
  height = 300,
  smooth = true,
  emptyText = '暂无数据',
  xAxisLabel = '时间',
  yAxisLabel = '数量',
  valueUnit = '',
  isStack = false,
  color,
  areaStyle,
  isTimeAxis = false,
  dateFormat = 'M/D',
}) => {
  if (!data || data.length === 0) {
    return <Empty description={emptyText} />;
  }

  // 检查是否为多系列数据
  const isMultiSeries = data.some((item) => item.type !== undefined);

  const config = {
    data,
    xField: 'period',
    yField: 'value',
    ...(isMultiSeries && { seriesField: 'type' }),
    height,
    smooth,
    ...(isStack && isMultiSeries && { isStack: true }),

    // 颜色配置
    ...(color && {
      color: Array.isArray(color) ? color : [color],
    }),

    // 区域样式配置
    ...(areaStyle && { areaStyle }),

    // 默认区域样式（如果没有自定义）
    ...(!areaStyle &&
      !isMultiSeries && {
        areaStyle: {
          fill: 'linear-gradient(-90deg, white 0%, #1890ff 100%)',
          fillOpacity: 0.6,
        },
      }),

    // 图例配置（多系列时显示）
    ...(isMultiSeries && {
      legend: {
        position: 'bottom',
      },
    }),

    // X轴配置
    xAxis: {
      title: {
        text: xAxisLabel,
        style: {
          fontSize: 12,
        },
      },
      ...(isTimeAxis && {
        type: 'time',
        label: {
          formatter: (text: string) => {
            const date = new Date(text);
            if (dateFormat === 'M/D') {
              return `${date.getMonth() + 1}/${date.getDate()}`;
            }
            return text;
          },
        },
      }),
      ...(!isTimeAxis && {
        label: {
          style: {
            fontSize: 11,
          },
        },
      }),
    },

    // Y轴配置
    yAxis: {
      title: {
        text: yAxisLabel,
        style: {
          fontSize: 12,
        },
      },
      nice: true,
      min: 0,
      label: {
        formatter: (text: string) => `${text}${valueUnit}`,
        style: {
          fontSize: 11,
        },
      },
    },

    // 动画配置
    animation: {
      appear: {
        animation: 'path-in',
        duration: 1000,
      },
    },

    // 滑块配置
    slider: {
      x: {},
    },
  };

  return <Area {...config} />;
};

export default AreaChart;

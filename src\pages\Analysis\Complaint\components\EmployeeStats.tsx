import * as complaints from '@/services/complaints';
import { ProCard, ProColumns, ProTable } from '@ant-design/pro-components';
import { Avatar, message, Progress, Rate, Space, Typography } from 'antd';
import { Dayjs } from 'dayjs';
import React from 'react';

const { Text } = Typography;

interface EmployeeStatsProps {
  dateRange: [Dayjs, Dayjs];
}

const EmployeeStats: React.FC<EmployeeStatsProps> = ({ dateRange }) => {
  const fetchData = async (params: any) => {
    try {
      const {
        errCode,
        msg,
        data: responseData,
      } = await complaints.employeeStats({
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD'),
        page: params.current,
        pageSize: params.pageSize,
        sortBy: params.sortBy || 'complaintCount',
        sortOrder: params.sortOrder || 'desc',
      });

      if (errCode) {
        message.error(msg || '获取员工统计数据失败');
        return {
          data: [],
          total: 0,
          success: false,
        };
      }

      if (responseData) {
        return {
          data: responseData.list,
          total: responseData.total,
          success: true,
        };
      }

      return {
        data: [],
        total: 0,
        success: false,
      };
    } catch (error) {
      console.error('获取员工统计数据失败:', error);
      message.error('获取员工统计数据失败');
      return {
        data: [],
        total: 0,
        success: false,
      };
    }
  };

  const columns: ProColumns<API.ComplaintEmployeeStats>[] = [
    {
      title: '员工信息',
      dataIndex: 'employeeName',
      width: 200,
      render: (_: any, record: API.ComplaintEmployeeStats) => (
        <Space>
          <Avatar src={record.employeeAvatar} size="small">
            {record.employeeName?.charAt(0)}
          </Avatar>
          <div>
            <div>{record.employeeName || '未知员工'}</div>
            <Text type="secondary" style={{ fontSize: 12 }}>
              {record.employeePhone}
            </Text>
          </div>
        </Space>
      ),
    },
    {
      title: '员工评分',
      dataIndex: 'employeeRating',
      width: 120,
      render: (_rating, record) => (
        <Space>
          <Rate
            disabled
            value={record.employeeRating}
            allowHalf
            style={{ fontSize: 14 }}
          />
          <Text type="secondary">({record.employeeRating?.toFixed(1)})</Text>
        </Space>
      ),
    },
    {
      title: '被投诉次数',
      dataIndex: 'complaintCount',
      width: 120,
      sorter: true,
      render: (count, record) => (
        <Text
          strong
          style={{ color: record.complaintCount > 0 ? '#ff4d4f' : '#52c41a' }}
        >
          {count}
        </Text>
      ),
    },
    {
      title: '已解决投诉',
      dataIndex: 'resolvedCount',
      width: 120,
      sorter: true,
      render: (count) => <Text style={{ color: '#722ed1' }}>{count}</Text>,
    },
    {
      title: '待处理投诉',
      dataIndex: 'pendingCount',
      width: 120,
      render: (count, record) => (
        <Text
          style={{ color: record.resolvedCount > 0 ? '#faad14' : '#52c41a' }}
        >
          {count}
        </Text>
      ),
    },
    {
      title: '解决率',
      dataIndex: 'resolveRate',
      width: 150,
      sorter: true,
      render: (_rate, record) => {
        // 如果没有投诉，显示 N/A
        if (record.complaintCount === 0) {
          return <Text type="secondary">N/A</Text>;
        }

        return (
          <div style={{ width: 120 }}>
            <Progress
              percent={record.resolveRate}
              size="small"
              strokeColor={
                record.resolveRate >= 80
                  ? '#52c41a'
                  : record.resolveRate >= 60
                  ? '#faad14'
                  : '#ff4d4f'
              }
              format={(percent) => `${percent?.toFixed(1)}%`}
            />
          </div>
        );
      },
    },
  ];

  return (
    <ProCard title="员工投诉统计">
      <ProTable<API.ComplaintEmployeeStats>
        columns={columns}
        request={fetchData}
        rowKey="employeeId"
        search={false}
        pagination={{
          defaultPageSize: 20,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
        }}
        scroll={{ x: 800 }}
        size="small"
        options={{
          reload: true,
          density: false,
          setting: false,
        }}
        params={{ dateRange }}
        toolBarRender={false}
      />
    </ProCard>
  );
};

export default EmployeeStats;

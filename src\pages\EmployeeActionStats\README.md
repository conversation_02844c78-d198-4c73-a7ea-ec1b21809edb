# 员工操作统计

## 功能概述

员工操作统计是一个专门为管理端提供的员工关键动作分析系统，基于订单状态变更记录统计员工的关键操作行为，帮助管理员了解员工工作效率和服务质量。

## 主要功能

### 1. 统计概览

- **总体统计**：显示指定时间范围内的总动作数和动作类型数
- **动作类型统计**：按动作类型统计执行次数和占比
- **筛选功能**：支持按时间范围和员工筛选
- **可视化展示**：使用统计卡片和表格展示数据

### 2. 动作记录列表

- **详细记录**：显示所有员工的动作记录详情
- **多维筛选**：支持按员工、时间范围、订单 ID、动作类型筛选
- **信息展示**：显示订单信息、员工信息、客户信息、动作类型等
- **分页查询**：支持分页浏览和快速跳转

### 3. 员工统计

- **按员工统计**：统计每个员工的总动作数和动作分解
- **可展开详情**：点击行可展开查看详细动作分解
- **排序功能**：支持按总动作数排序
- **占比分析**：显示每种动作类型的执行次数和占比

### 4. 动作时间线

- **时间线展示**：按时间顺序展示员工的动作记录
- **员工筛选**：必须选择员工才能查看时间线
- **订单筛选**：可选择特定订单查看相关动作
- **详细信息**：显示动作类型、订单编号、执行时间和描述

## 动作类型说明

系统统计以下关键动作类型：

- **接单**：员工接受订单
- **派单**：系统派发订单给员工
- **转单**：订单转给其他员工
- **修改服务时间**：修改预约服务时间
- **出发**：员工出发前往服务地点
- **开始服务**：员工开始提供服务
- **完成订单**：员工完成服务
- **取消订单**：取消订单
- **申请退款**：申请退款
- **退款**：处理退款

## 技术实现

### 组件结构

```
src/pages/EmployeeActionStats/
├── index.tsx                    # 主入口文件
├── components/
│   ├── index.ts                # 组件导出
│   ├── ActionOverview.tsx      # 统计概览
│   ├── ActionLogList.tsx       # 动作记录列表
│   ├── ActionStatsByEmployee.tsx # 员工统计
│   └── ActionTimeline.tsx      # 动作时间线
└── README.md                   # 功能说明
```

### API 接口

- **GET /employee-action-logs/list** - 获取员工动作记录列表
- **GET /employee-action-logs/overview** - 获取员工动作统计概览
- **GET /employee-action-logs/stats-by-employee** - 按员工统计动作记录
- **GET /employee-action-logs/timeline** - 获取员工动作时间线

### 数据来源

- 基于 `service_change_logs` 表中 `employeeId` 不为空的记录
- 只统计员工执行的动作，不包括客户操作
- 按动作执行时间排序

## 使用说明

1. **访问路径**：系统管理 → 员工操作统计
2. **权限要求**：需要相应的访问权限（access: '071000'）
3. **筛选建议**：建议先设置时间范围，再选择具体员工进行分析
4. **时间线查看**：必须先选择员工才能查看动作时间线
5. **数据分析**：可结合不同维度的统计数据进行员工工作效率分析

## 注意事项

1. 所有时间均为服务器时间（UTC+8）
2. 动作统计可以用于分析员工工作效率和服务质量
3. 时间线接口按动作时间正序排列，其他接口按时间倒序排列
4. 获取员工动作时间线接口必须提供员工 ID 参数
5. 数据基于订单状态变更日志，确保数据的准确性和完整性

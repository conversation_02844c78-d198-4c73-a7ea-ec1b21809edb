import {
  ModalForm,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { AutoComplete, AutoCompleteProps } from 'antd';
import React from 'react';

type EditModalProps = {
  open: boolean;
  info?: API.Permission;
  currentFeature: API.Feature;
  onSave: (info: API.Permission) => Promise<void>;
  onClose: () => void;
};

const EditModal: React.FC<EditModalProps> = ({
  open,
  info,
  currentFeature,
  onSave,
  onClose,
}) => {
  const [options, setOptions] = React.useState<AutoCompleteProps['options']>(
    [],
  );

  return (
    <ModalForm<API.Permission>
      title={info ? '编辑权限点' : '注册权限点'}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: onClose,
      }}
      open={open}
      layout="horizontal"
      grid
      labelCol={{ flex: '7em' }}
      onFinish={onSave}
    >
      <ProFormText name="id" label="ID" hidden initialValue={info?.id} />
      <ProFormText
        name="name"
        label="名称"
        fieldProps={{ maxLength: 20 }}
        rules={[{ required: true, message: '请输入名称！' }]}
        colProps={{ span: 12 }}
        initialValue={info?.name || ''}
      >
        <AutoComplete
          options={options}
          onSearch={(value) => {
            setOptions(() => {
              if (!value) {
                return [];
              }
              return ['查看', '使用', '管理']
                .filter((item) => {
                  return item.includes(value);
                })
                .map((item) => {
                  return {
                    value: item,
                    label: item,
                  };
                });
            });
          }}
        />
      </ProFormText>
      <ProFormTextArea
        name="description"
        label="描述"
        fieldProps={{
          maxLength: 100,
          showCount: true,
        }}
        colProps={{ span: 24 }}
        initialValue={info?.description || ''}
      />
      <ProFormText
        name="featureCode"
        label="所属功能点"
        initialValue={info?.featureCode || currentFeature.code}
        hidden
      />
    </ModalForm>
  );
};

export default EditModal;

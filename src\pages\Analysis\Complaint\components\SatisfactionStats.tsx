import * as complaints from '@/services/complaints';
import { Pie } from '@ant-design/charts';
import { ProCard } from '@ant-design/pro-components';
import { Card, Col, message, Row, Select, Space, Statistic } from 'antd';
import { Dayjs } from 'dayjs';
import React, { useEffect, useState } from 'react';

interface SatisfactionStatsProps {
  dateRange: [Dayjs, Dayjs];
}

const SatisfactionStats: React.FC<SatisfactionStatsProps> = ({ dateRange }) => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<API.ComplaintSatisfactionStats>({
    total: 0,
    satisfiedCount: 0,
    unsatisfiedCount: 0,
    neutralCount: 0,
    satisfactionRate: 0,
    unsatisfactionRate: 0,
    neutralRate: 0,
  });
  const [category, setCategory] = useState<string | undefined>(undefined);
  const [subCategory, setSubCategory] = useState<string | undefined>(undefined);

  const fetchData = async () => {
    setLoading(true);
    try {
      const {
        errCode,
        msg,
        data: responseData,
      } = await complaints.satisfaction({
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD'),
        category: category as API.ComplaintCategory,
        subCategory: subCategory as API.ComplaintSubCategory,
      });

      if (errCode) {
        message.error(msg || '获取满意度数据失败');
        return;
      }

      if (responseData) {
        setData(responseData);
      }
    } catch (error) {
      console.error('获取满意度数据失败:', error);
      message.error('获取满意度数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [dateRange, category, subCategory]);

  // 转换数据格式用于饼图
  const pieData = [
    {
      type: '满意',
      value: data.satisfiedCount,
      percentage: data.satisfactionRate,
    },
    {
      type: '不满意',
      value: data.unsatisfiedCount,
      percentage: data.unsatisfactionRate,
    },
    {
      type: '中性',
      value: data.neutralCount,
      percentage: data.neutralRate,
    },
  ].filter((item) => item.value > 0);

  const config = {
    data: pieData,
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    color: ['#52c41a', '#ff4d4f', '#faad14'],
    label: {
      type: 'outer',
      content: '{name} {percentage}%',
    },
    interactions: [
      {
        type: 'element-active',
      },
    ],
    legend: {
      position: 'bottom' as const,
    },
    tooltip: {
      formatter: (datum: any) => {
        return {
          name: datum.type,
          value: `${datum.value} (${datum.percentage.toFixed(1)}%)`,
        };
      },
    },
  };

  return (
    <ProCard
      title="满意度统计"
      loading={loading}
      extra={
        <Space>
          <Select
            placeholder="选择类型"
            value={category}
            onChange={setCategory}
            style={{ width: 100 }}
            allowClear
            options={[
              { label: '投诉', value: 'complaint' },
              { label: '建议', value: 'suggestion' },
            ]}
          />
          <Select
            placeholder="选择子类型"
            value={subCategory}
            onChange={setSubCategory}
            style={{ width: 120 }}
            allowClear
            options={[
              { label: '订单投诉', value: 'order' },
              { label: '人员投诉', value: 'employee' },
              { label: '平台建议', value: 'platform' },
              { label: '服务建议', value: 'service' },
            ]}
          />
        </Space>
      }
    >
      {/* 满意度统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col xs={12} sm={6}>
          <Card size="small" style={{ textAlign: 'center' }}>
            <Statistic
              title="总处理数量"
              value={data.total}
              valueStyle={{ color: '#1890ff', fontSize: '16px' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card size="small" style={{ textAlign: 'center' }}>
            <Statistic
              title="满意率"
              value={data.satisfactionRate}
              precision={1}
              suffix="%"
              valueStyle={{ color: '#52c41a', fontSize: '16px' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card size="small" style={{ textAlign: 'center' }}>
            <Statistic
              title="不满意率"
              value={data.unsatisfactionRate}
              precision={1}
              suffix="%"
              valueStyle={{ color: '#ff4d4f', fontSize: '16px' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card size="small" style={{ textAlign: 'center' }}>
            <Statistic
              title="中性率"
              value={data.neutralRate}
              precision={1}
              suffix="%"
              valueStyle={{ color: '#faad14', fontSize: '16px' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 满意度分布饼图 */}
      {pieData.length > 0 ? (
        <Pie {...config} height={250} />
      ) : (
        <div style={{ textAlign: 'center', padding: '50px 0', color: '#999' }}>
          暂无满意度数据
        </div>
      )}
    </ProCard>
  );
};

export default SatisfactionStats;

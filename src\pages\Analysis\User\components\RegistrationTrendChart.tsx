import { AreaChart, AreaChartData } from '@/components/Charts';
import React from 'react';

interface RegistrationTrendChartProps {
  data: API.CustomerRegistrationTrend[];
}

const RegistrationTrendChart: React.FC<RegistrationTrendChartProps> = ({
  data,
}) => {
  // 转换数据格式为 AreaChart 组件需要的格式
  const chartData: AreaChartData[] = data.map((item) => ({
    period: item.period,
    value: Number(item.count), // 确保count是数字类型
  }));

  return (
    <AreaChart
      data={chartData}
      height={350}
      emptyText="暂无注册数据"
      xAxisLabel="时间"
      yAxisLabel="注册人数"
      valueUnit="人"
      smooth={true}
      isTimeAxis={true}
      dateFormat="M/D"
      color="#1890ff"
      areaStyle={{
        fill: 'linear-gradient(-90deg, white 0%, #1890ff 100%)',
        fillOpacity: 0.6,
      }}
    />
  );
};

export default RegistrationTrendChart;

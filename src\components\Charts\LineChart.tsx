import { Line } from '@ant-design/plots';
import { Empty } from 'antd';
import React from 'react';

export interface LineChartData {
  period: string;
  value: number;
  type: string;
}

interface LineChartProps {
  data: LineChartData[];
  height?: number;
  smooth?: boolean;
  showPoint?: boolean;
  emptyText?: string;
  xAxisLabel?: string;
  yAxisLabel?: string;
  valueUnit?: string;
}

const LineChart: React.FC<LineChartProps> = ({
  data,
  height = 300,
  smooth = true,
  showPoint = true,
  emptyText = '暂无数据',
  xAxisLabel = '时间',
  yAxisLabel = '数量',
  valueUnit = '',
}) => {
  if (!data || data.length === 0) {
    return <Empty description={emptyText} />;
  }

  console.log('LineChart data:', data);
  const config = {
    data,
    xField: 'period',
    yField: 'value',
    seriesField: 'type',
    height,
    smooth,
    scale: {
      color: {
        palette: 'rainbow',
      },
    },
    legend: {
      position: 'bottom',
      // offsetY: -5,
    },
    ...(showPoint && {
      point: {
        size: 4,
        shape: 'circle',
      },
    }),
    xAxis: {
      title: {
        text: xAxisLabel,
        style: {
          fontSize: 12,
        },
      },
      label: {
        style: {
          fontSize: 11,
        },
      },
    },
    yAxis: {
      title: {
        text: yAxisLabel,
        style: {
          fontSize: 12,
        },
      },
      label: {
        formatter: (text: string) => `${text}${valueUnit}`,
        style: {
          fontSize: 11,
        },
      },
    },
    animation: {
      appear: {
        animation: 'path-in',
        duration: 1000,
      },
    },
    slider: {
      x: {},
    },
  };

  return <Line {...config} />;
};

export default LineChart;

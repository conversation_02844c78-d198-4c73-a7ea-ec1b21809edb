.mapWrapper {
  width: 100%;
  height: 100%;
  min-height: 400px;
  position: relative;
}

.mapContainer {
  width: 100%;
  height: 100%;
  min-height: 400px;
  position: relative;

  /* 确保地图容器有明确的尺寸和位置 */
  display: block;
  overflow: hidden;
}

/* 加载状态样式 */
.mapLoading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 70%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.loadingText {
  padding: 10px 20px;
  background-color: rgba(0, 0, 0, 60%);
  color: white;
  border-radius: 4px;
  font-size: 14px;
}

/* 错误状态样式 */
.mapError {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 90%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.errorText {
  padding: 10px 20px;
  background-color: rgba(220, 53, 69, 80%);
  color: white;
  border-radius: 4px;
  font-size: 14px;
}

/* 自定义控件样式 */
.customControl {
  position: absolute;
  z-index: 100;
  background-color: white;
  border-radius: 2px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 30%);
  overflow: hidden;
  margin: 10px;
  padding: 5px;
}

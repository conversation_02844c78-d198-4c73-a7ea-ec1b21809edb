import { CopyOutlined } from '@ant-design/icons';
import { Button, Divider, Image, Modal, Tag, Typography } from 'antd';
import React from 'react';

const { Title, Paragraph } = Typography;

interface ProcessGuideModalProps {
  visible: boolean;
  onClose: () => void;
}

const ProcessGuideModal: React.FC<ProcessGuideModalProps> = ({
  visible,
  onClose,
}) => {
  const mermaidDiagram = `stateDiagram-v2
    [*] --> pending: 用户提交
    pending --> processing: 管理员开始处理
    pending --> closed: 管理员直接关闭
    processing --> resolved: 处理完成
    processing --> closed: 处理后关闭
    resolved --> closed: 确认解决

    note right of pending
        待处理状态
        用户可修改/删除
    end note

    note right of processing
        处理中状态
        记录处理人员
    end note

    note right of resolved
        已解决状态
        记录处理结果
    end note

    note right of closed
        已关闭状态
        流程结束
    end note`;

  const handleCopyDiagram = async () => {
    try {
      await navigator.clipboard.writeText(mermaidDiagram);
      // 可以添加成功提示
    } catch (err) {
      console.error('复制失败:', err);
    }
  };

  return (
    <Modal
      title="投诉建议处理流程说明"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={900}
      destroyOnClose
    >
      <div style={{ padding: '16px 0' }}>
        {/* <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}> */}
        <div
          style={{
            display: 'none',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Title level={4}>处理流程图</Title>
          <Button
            icon={<CopyOutlined />}
            size="small"
            onClick={handleCopyDiagram}
          >
            复制 Mermaid 代码
          </Button>
        </div>
        <Paragraph>投诉建议处理遵循以下状态流转规则：</Paragraph>

        {/* 流程图显示区域 */}
        <div
          style={{
            display: 'none',
            margin: '24px 0',
            padding: '20px',
            background: '#fafafa',
            border: '1px solid #d9d9d9',
            borderRadius: '8px',
          }}
        >
          <div
            style={{
              background: '#f6f8fa',
              border: '1px solid #d1d9e0',
              borderRadius: '8px',
              padding: '16px',
              fontFamily: 'monospace',
              fontSize: '12px',
              lineHeight: '1.6',
              overflow: 'auto',
            }}
          >
            <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
              {mermaidDiagram}
            </pre>
          </div>

          {/* 流程图可视化说明 */}
          <div
            style={{
              marginTop: '16px',
              padding: '12px',
              background: '#e6f7ff',
              border: '1px solid #91d5ff',
              borderRadius: '6px',
            }}
          >
            <Paragraph style={{ margin: 0, fontSize: '13px' }}>
              <strong>📊 如何查看可视化流程图：</strong>
            </Paragraph>
            <Paragraph
              style={{ margin: '8px 0 0 0', fontSize: '12px', color: '#666' }}
            >
              1. 点击上方&quot;复制 Mermaid 代码&quot;按钮
              <br />
              2. 访问{' '}
              <a
                href="https://mermaid.live"
                target="_blank"
                rel="noopener noreferrer"
              >
                mermaid.live
              </a>{' '}
              在线编辑器
              <br />
              3. 粘贴代码即可查看精美的可视化流程图
              <br />
              4. 支持导出为 PNG、SVG 等格式
            </Paragraph>
          </div>
        </div>
        <div
          style={{
            margin: '24px 0',
            padding: '20px',
            background: '#fafafa',
            border: '1px solid #d9d9d9',
            borderRadius: '8px',
          }}
        >
          <Image src="/flow_tsjy.png" />
        </div>

        <Divider />

        <Title level={5}>状态说明</Title>
        <div style={{ marginLeft: '16px' }}>
          <Paragraph>
            <Tag color="orange">待处理</Tag>
            新提交的投诉建议，等待管理员处理。此状态下用户可以修改或删除记录。
          </Paragraph>
          <Paragraph>
            <Tag color="blue">处理中</Tag>
            管理员已开始处理，会记录处理人员信息。
          </Paragraph>
          <Paragraph>
            <Tag color="green">已解决</Tag>
            问题已经得到解决，需要记录详细的处理结果。
          </Paragraph>
          <Paragraph>
            <Tag color="gray">已关闭</Tag>
            流程结束，不再进行后续处理。
          </Paragraph>
        </div>

        <Divider />

        <Title level={5}>状态流转规则</Title>
        <div style={{ marginLeft: '16px' }}>
          <Paragraph>
            <strong>1. 待处理 → 处理中</strong>：管理员开始处理投诉建议
          </Paragraph>
          <Paragraph>
            <strong>2. 待处理 → 已关闭</strong>
            ：管理员直接关闭（如重复投诉、无效投诉等）
          </Paragraph>
          <Paragraph>
            <strong>3. 处理中 → 已解决</strong>：问题处理完成，等待确认
          </Paragraph>
          <Paragraph>
            <strong>4. 处理中 → 已关闭</strong>：处理后直接关闭
          </Paragraph>
          <Paragraph>
            <strong>5. 已解决 → 已关闭</strong>：确认解决后关闭流程
          </Paragraph>
        </div>

        <Divider />

        <Title level={5}>操作权限</Title>
        <div style={{ marginLeft: '16px' }}>
          <Paragraph>
            • <strong>待处理状态</strong>
            ：可查看详情、开始处理、直接关闭、删除记录
          </Paragraph>
          <Paragraph>
            • <strong>处理中状态</strong>：可查看详情、标记为已解决、直接关闭
          </Paragraph>
          <Paragraph>
            • <strong>已解决状态</strong>：可查看详情、确认关闭
          </Paragraph>
          <Paragraph>
            • <strong>已关闭状态</strong>：仅可查看详情
          </Paragraph>
        </div>

        <Divider />

        <Title level={5}>注意事项</Title>
        <div style={{ marginLeft: '16px' }}>
          <Paragraph>• 只有待处理状态的投诉建议可以被删除</Paragraph>
          <Paragraph>• 处理结果在状态为已解决或已关闭时为必填项</Paragraph>
          <Paragraph>• 所有状态变更都会记录操作人员和操作时间</Paragraph>
          <Paragraph>• 已关闭的投诉建议不能再次修改状态</Paragraph>
        </div>
      </div>
    </Modal>
  );
};

export default ProcessGuideModal;

import OptimizedServiceDurationView from '@/components/ServiceDurationStatistics/OptimizedView';
import { getServiceDurationStatistics } from '@/services/service-duration-statistics';
import { DatePicker, message, Modal } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import React, { useEffect, useState } from 'react';

const { RangePicker } = DatePicker;

interface ServiceDurationModalProps {
  open: boolean;
  service?: API.Service;
  onClose: () => void;
}

const ServiceDurationModal: React.FC<ServiceDurationModalProps> = ({
  open,
  service,
  onClose,
}) => {
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] =
    useState<API.ServiceDurationStatisticsResponse>();
  const [dateRange, setDateRange] = useState<[Dayjs, Dayjs]>([
    dayjs().subtract(30, 'day'),
    dayjs(),
  ]);

  // 获取服务时长统计
  const fetchStatistics = async (
    serviceId: number,
    startDate?: string,
    endDate?: string,
  ) => {
    setLoading(true);
    try {
      const { errCode, msg, data } = await getServiceDurationStatistics(
        serviceId,
        {
          startDate,
          endDate,
          page: 1,
          pageSize: 50,
        },
      );
      if (errCode) {
        message.error(msg || '获取时长统计失败');
      } else {
        setStatistics(data);
      }
    } catch (error) {
      console.error('获取时长统计失败:', error);
      message.error('获取时长统计失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 当弹窗打开且有服务信息时，获取统计数据
  useEffect(() => {
    if (open && service?.id) {
      const [startDate, endDate] = dateRange;
      fetchStatistics(
        service.id,
        startDate.format('YYYY-MM-DD'),
        endDate.format('YYYY-MM-DD'),
      );
    }
  }, [open, service?.id, dateRange]);

  // 处理日期范围变化
  const handleDateRangeChange = (dates: [Dayjs, Dayjs] | null) => {
    if (dates) {
      setDateRange(dates);
    }
  };

  return (
    <Modal
      title={`服务时长统计 - ${service?.serviceName || ''}`}
      open={open}
      onCancel={onClose}
      width={1400}
      footer={null}
      destroyOnClose
    >
      <div style={{ marginBottom: 16 }}>
        <RangePicker
          value={dateRange}
          onChange={handleDateRangeChange}
          format="YYYY-MM-DD"
          placeholder={['开始日期', '结束日期']}
        />
      </div>

      {statistics && (
        <OptimizedServiceDurationView
          serviceStatistics={statistics}
          loading={loading}
          title={`${service?.serviceName} 时长统计详情`}
          showOrderInfo={true}
          showEmployeeInfo={true}
        />
      )}
    </Modal>
  );
};

export default ServiceDurationModal;

/*
 * @Description: 追加服务订单
 */
import { request } from '@umijs/max';

/** 查询追加服务列表  GET /order-details/{orderDetailId}/additional-services */
export async function getAdditionalServices(
  orderDetailId: number,
  params?: { status?: string },
) {
  return request<API.ResType<API.AdditionalServiceResponse>>(
    `/order-details/${orderDetailId}/additional-services`,
    {
      method: 'GET',
      params,
    },
  );
}

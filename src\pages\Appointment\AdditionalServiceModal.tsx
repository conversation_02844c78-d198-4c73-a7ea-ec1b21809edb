import { getAdditionalServices } from '@/services/additional-service-orders';
import { Descriptions, Modal, Spin, Table, Tag, message } from 'antd';
import { ColumnsType } from 'antd/es/table';
import React, { useEffect, useState } from 'react';

// 追加服务状态工具函数
const AdditionalServiceStatusUtils = {
  getStatusColor: (status: string) => {
    switch (status) {
      case 'pending_confirm':
        return 'orange';
      case 'confirmed':
        return 'blue';
      case 'rejected':
        return 'red';
      case 'pending_payment':
        return 'gold';
      case 'paid':
        return 'processing';
      case 'completed':
        return 'success';
      case 'cancelled':
        return 'default';
      case 'refunding':
        return 'warning';
      case 'refunded':
        return 'error';
      default:
        return 'default';
    }
  },

  getStatusText: (status: string) => {
    switch (status) {
      case 'pending_confirm':
        return '待确认';
      case 'confirmed':
        return '已确认';
      case 'rejected':
        return '已拒绝';
      case 'pending_payment':
        return '待付款';
      case 'paid':
        return '已付款/服务中';
      case 'completed':
        return '已完成';
      case 'cancelled':
        return '已取消';
      case 'refunding':
        return '退款中';
      case 'refunded':
        return '已退款';
      default:
        return status;
    }
  },
};

interface AdditionalServiceModalProps {
  visible: boolean;
  orderDetailId?: number;
  onClose: () => void;
}

const AdditionalServiceModal: React.FC<AdditionalServiceModalProps> = ({
  visible,
  orderDetailId,
  onClose,
}) => {
  const [loading, setLoading] = useState(false);
  const [additionalServices, setAdditionalServices] = useState<
    API.AdditionalServiceOrder[]
  >([]);

  const fetchAdditionalServices = async () => {
    if (!orderDetailId) return;

    setLoading(true);
    try {
      const { errCode, msg, data } = await getAdditionalServices(orderDetailId);
      if (errCode) {
        message.error(msg || '获取追加服务列表失败');
      } else {
        // 处理新的数据结构，支持向后兼容
        if (data) {
          // 新的数据结构：包含 additionalServiceOrders 字段
          if ('additionalServiceOrders' in data) {
            setAdditionalServices(data.additionalServiceOrders || []);
          }
          // 旧的数据结构：直接是数组
          else if (Array.isArray(data)) {
            setAdditionalServices(data);
          }
          // 其他情况设为空数组
          else {
            setAdditionalServices([]);
          }
        } else {
          setAdditionalServices([]);
        }
      }
    } catch (error) {
      console.error('获取追加服务失败:', error);
      message.error('获取追加服务失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible && orderDetailId) {
      fetchAdditionalServices();
    }
  }, [visible, orderDetailId]);

  const columns: ColumnsType<API.AdditionalServiceOrder> = [
    {
      title: '订单编号',
      dataIndex: 'sn',
      key: 'sn',
      width: 180,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={AdditionalServiceStatusUtils.getStatusColor(status)}>
          {AdditionalServiceStatusUtils.getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '原价',
      dataIndex: 'originalPrice',
      key: 'originalPrice',
      width: 100,
      render: (price: number) => `¥${price || 0}`,
    },
    {
      title: '实付金额',
      dataIndex: 'totalFee',
      key: 'totalFee',
      width: 100,
      render: (fee: number) => `¥${fee || 0}`,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (time: string) => new Date(time).toLocaleString(),
    },
  ];

  const expandedRowRender = (record: API.AdditionalServiceOrder) => {
    const detailColumns: ColumnsType<API.AdditionalServiceOrderDetail> = [
      {
        title: '服务名称',
        dataIndex: 'serviceName',
        key: 'serviceName',
      },
      {
        title: '服务价格',
        dataIndex: 'servicePrice',
        key: 'servicePrice',
        render: (price: number) => `¥${price || 0}`,
      },
      {
        title: '数量',
        dataIndex: 'quantity',
        key: 'quantity',
      },
      {
        title: '小计',
        key: 'subtotal',
        render: (_, detail) =>
          `¥${(detail.servicePrice || 0) * (detail.quantity || 0)}`,
      },
    ];

    return (
      <div style={{ margin: '16px 0' }}>
        <Descriptions
          title="客户信息"
          size="small"
          column={3}
          style={{ marginBottom: 16 }}
        >
          <Descriptions.Item label="客户姓名">
            {record.customer?.nickname || '未知'}
          </Descriptions.Item>
          <Descriptions.Item label="联系电话">
            {record.customer?.phone || '未知'}
          </Descriptions.Item>
        </Descriptions>
        <Table
          columns={detailColumns}
          dataSource={record.details || []}
          pagination={false}
          size="small"
          rowKey="id"
          title={() => '服务详情'}
        />
      </div>
    );
  };

  return (
    <Modal
      title="追加服务详情"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1000}
      destroyOnClose
    >
      <Spin spinning={loading}>
        {additionalServices.length > 0 ? (
          <Table
            columns={columns}
            dataSource={additionalServices}
            pagination={false}
            rowKey="id"
            expandable={{
              expandedRowRender,
              defaultExpandAllRows: false,
            }}
          />
        ) : (
          <div
            style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}
          >
            暂无追加服务
          </div>
        )}
      </Spin>
    </Modal>
  );
};

export default AdditionalServiceModal;

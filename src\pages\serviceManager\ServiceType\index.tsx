import { DictionarieState } from '@/models/dictionarie';
import { create, index, remove, update } from '@/services/service-type';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { connect } from '@umijs/max';
import { But<PERSON>, message, Modal, Popconfirm, Space } from 'antd';
import React, { useRef, useState } from 'react';
import EditModal from './EditModal';

const ServiceType: React.FC<{ dictionarie: DictionarieState }> = ({
  dictionarie,
}) => {
  const actionRef = useRef<ActionType>();
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [current, setCurrent] = useState<API.ServiceType | undefined>(
    undefined,
  );

  const serviceTypeList =
    dictionarie?.list?.filter((item) => item.type === '服务类型') || [];
  const typeValueEnum: { [key: string]: string } = {};
  serviceTypeList.forEach((item) => {
    typeValueEnum[item.code] = item.name;
  });
  console.log(typeValueEnum);

  const handleSave = async (values: API.ServiceType) => {
    let response;
    if (current) {
      const { id, ...info } = values;
      response = await update(id, info);
    } else {
      response = await create(values);
    }

    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('操作成功');
      actionRef?.current?.reload();
      setModalVisible(false);
    }
  };

  const handleDel = async (record: API.ServiceType) => {
    const { id } = record;
    const response = await remove(id);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('删除成功');
      actionRef?.current?.reload();
    }
  };

  const columns: ProColumns<API.ServiceType, 'text'>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      hidden: true,
      hideInSearch: true,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 100,
      fixed: 'left',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 50,
      hideInSearch: true,
      align: 'center',
      render: (_, entity) => {
        return (
          <Button
            type="link"
            onClick={() => {
              Modal.info({
                title: '描述',
                icon: null,
                width: 600,
                content: (
                  <div
                    style={{
                      width: '100%',
                      height: '100%',
                      overflow: 'auto',
                    }}
                    dangerouslySetInnerHTML={{
                      __html: entity.description || '',
                    }}
                  ></div>
                ),
              });
            }}
          >
            查看
          </Button>
        );
      },
    },
    {
      title: '服务类型',
      dataIndex: 'type',
      key: 'type',
      width: 60,
      align: 'center',
      valueEnum: typeValueEnum,
      filters: true,
    },
    {
      title: '排序值',
      dataIndex: 'orderIndex',
      key: 'orderIndex',
      hidden: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 130,
      valueType: 'option',
      align: 'center',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            onClick={() => {
              setCurrent(record);
              setModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确认删除？"
            onConfirm={() => {
              handleDel(record);
            }}
          >
            <Button type="link" danger>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <ProTable<API.ServiceType>
        actionRef={actionRef}
        rowKey="id"
        columns={columns}
        request={async (params, _sort, filter) => {
          const { errCode, msg, data } = await index({ ...params, filter });
          if (errCode) {
            message.error(msg || '列表查询失败');
            return {
              data: [],
              total: 0,
            };
          }
          return {
            data: data?.list || [],
            total: data?.total || 0,
          };
        }}
        scroll={{ x: '100%' }}
        toolBarRender={() => [
          <Button
            key="add"
            type="primary"
            onClick={() => {
              setCurrent(undefined);
              setModalVisible(true);
            }}
          >
            新增
          </Button>,
        ]}
      />
      <EditModal
        open={modalVisible}
        info={current}
        onClose={() => setModalVisible(false)}
        onSave={handleSave}
      />
    </>
  );
};

export default connect(({ dictionarie }) => ({ dictionarie }))(ServiceType);

# GaoDeMap 高德地图组件

一个基于高德地图 JavaScript API 的 React 封装组件，提供了简单易用的接口来在 React 应用中集成高德地图功能。

## 功能特性

- 🗺️ 简单易用的高德地图 React 组件
- 📍 支持标记点、信息窗体和自定义控件
- 🔍 支持地图事件监听和回调
- 📦 支持标记点聚合功能
- 🌐 支持城市名称定位和坐标定位
- 🔄 支持懒加载和自定义渲染
- 🛡️ 增强的异步操作安全检查

## 组件架构

组件采用 React Hooks 架构，主要分为三个核心部分：

```mermaid
graph TD
    A[GaoDeMap 组件] --> B[useMapAPI Hook]
    A --> C[useMapInstance Hook]
    B --> D[高德地图 JS API]
    C --> E[地图实例]
    C --> F[标记点管理]
    C --> G[事件处理]
    C --> H[自定义控件]
    E --> I[地图渲染]
    F --> J[标记点渲染]
    F --> K[信息窗体]
    F --> L[标记点聚合]
```

## 数据流程

```mermaid
sequenceDiagram
    participant App as 应用组件
    participant Map as GaoDeMap组件
    participant API as useMapAPI
    participant Instance as useMapInstance
    participant AMap as 高德地图API

    App->>Map: 传入props (center, markers等)
    Map->>API: 调用useMapAPI(enableCluster)
    API->>AMap: 加载高德地图JS API
    AMap-->>API: 返回AMap实例
    Map->>Instance: 调用useMapInstance(props)
    Instance->>AMap: 获取城市位置
    AMap-->>Instance: 返回位置坐标
    Instance->>AMap: 创建地图实例
    Instance->>AMap: 添加标记点
    Instance->>AMap: 设置事件监听
    Instance->>AMap: 添加自定义控件
    Instance-->>Map: 返回mapReady状态
    Map-->>App: 渲染地图
```

## 使用方法

### 基本用法

```jsx
import React from 'react';
import GaoDeMap from '@/components/GaoDeMap';

const MapExample = () => {
  // 定义标记点数据
  const markers = [
    {
      position: [116.397428, 39.90923], // 北京天安门坐标
      title: '天安门',
      content: '<div>天安门</div>',
    },
    {
      position: [121.473701, 31.230416], // 上海外滩坐标
      title: '上海外滩',
      content: '<div>上海外滩</div>',
    },
  ];

  // 定义地图事件处理函数
  const handleMapClick = (e) => {
    console.log('地图点击事件', e);
  };

  const handleMarkerClick = (marker, e) => {
    console.log('标记点点击事件', marker);
  };

  return (
    <div style={{ width: '100%', height: '500px' }}>
      <GaoDeMap
        city="北京"
        markers={markers}
        zoom={12}
        enableInfoWindow={true}
        events={{
          onClick: handleMapClick,
          onMarkerClick: handleMarkerClick,
        }}
      />
    </div>
  );
};

export default MapExample;
```

### 带自定义控件的地图

```jsx
import React from 'react';
import GaoDeMap from '@/components/GaoDeMap';

const MapWithControls = () => {
  // 自定义控件内容
  const zoomControl = (
    <div style={{ padding: '5px' }}>
      <button onClick={() => map.current?.zoomIn()}>+</button>
      <button onClick={() => map.current?.zoomOut()}>-</button>
    </div>
  );

  // 地图实例引用
  const map = React.useRef(null);

  return (
    <div style={{ width: '100%', height: '500px' }}>
      <GaoDeMap
        city="上海"
        zoom={11}
        onMapCreated={(mapInstance) => {
          map.current = mapInstance;
        }}
        customControls={[
          {
            position: 'RT', // 右上角
            content: zoomControl,
            offset: [10, 10],
          },
        ]}
      />
    </div>
  );
};

export default MapWithControls;
```

## API 参数说明

### GaoDeMap 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| center | [number, number] | - | 地图中心点坐标 [经度, 纬度] |
| city | string | '西安' | 城市名称，如果没有 center 则通过城市定位 |
| markers | Array<Marker> | [] | 标记点数组 |
| zoom | number | 11 | 地图缩放级别 |
| activedZoom | number | 15 | 激活标记点时的缩放级别 |
| enableCluster | boolean | false | 是否启用标记点聚合 |
| enableInfoWindow | boolean | true | 是否启用信息窗体 |
| events | MapEvents | {} | 地图事件回调 |
| loadingText | string | '地图加载中...' | 加载中文本 |
| onMapCreated | (mapInstance: any) => void | - | 地图创建完成回调 |
| customControls | Array<CustomControl> | [] | 自定义控件 |
| lazyLoading | boolean | false | 是否启用懒加载 |
| timeoutMs | number | 5000 | 超时时间（毫秒） |
| markerRender | (marker: Marker) => string | - | 自定义标记点渲染函数 |

### Marker 标记点属性

| 属性名 | 类型 | 说明 |
| --- | --- | --- |
| position | [number, number] | 标记点坐标 [经度, 纬度] |
| title | string | 标记点标题 |
| actived | boolean | 是否激活 |
| status | number | 状态码，用于支持不同状态的标记点 |
| icon | string \| MarkerIcon | 标记点图标，可以是图片 URL 或自定义图标对象 |
| content | string | 信息窗体内容 |
| id | string \| number | 标记点唯一标识 |

### MarkerIcon 标记点图标属性

| 属性名      | 类型             | 说明                   |
| ----------- | ---------------- | ---------------------- |
| image       | string           | 图标图片 URL           |
| size        | [number, number] | 图标大小 [宽度, 高度]  |
| offset      | [number, number] | 图标偏移量 [x, y]      |
| imageOffset | [number, number] | 图片偏移量，用于雪碧图 |
| imageSize   | [number, number] | 图片大小，用于雪碧图   |

### MapEvents 地图事件回调

| 属性名        | 类型                             | 说明             |
| ------------- | -------------------------------- | ---------------- |
| onClick       | (e: any) => void                 | 地图点击事件     |
| onMarkerClick | (marker: Marker, e: any) => void | 标记点点击事件   |
| onDragEnd     | (e: any) => void                 | 拖拽结束事件     |
| onZoomChange  | (zoom: number) => void           | 缩放级别变化事件 |

### CustomControl 自定义控件属性

| 属性名   | 类型                         | 说明                             |
| -------- | ---------------------------- | -------------------------------- |
| position | 'LT' \| 'RT' \| 'LB' \| 'RB' | 控件位置：左上、右上、左下、右下 |
| content  | React.ReactNode              | 控件内容                         |
| offset   | [number, number]             | 偏移量 [x, y]                    |

## 组件内部工作原理

### 1. 高德地图 API 加载 (useMapAPI)

```mermaid
flowchart TD
    A[组件挂载] --> B{是否已加载API?}
    B -->|是| C[返回AMap实例]
    B -->|否| D[设置安全密钥]
    D --> E[准备插件列表]
    E --> F{是否启用聚合?}
    F -->|是| G[添加聚合插件]
    F -->|否| H[不添加聚合插件]
    G --> I[加载高德地图JS API]
    H --> I
    I --> J{加载成功?}
    J -->|是| K[设置AMap实例]
    J -->|否| L[设置错误信息]
    K --> C
    L --> M[返回错误状态]
```

### 2. 地图实例创建和管理 (useMapInstance)

```mermaid
flowchart TD
    A[组件挂载] --> B{AMap是否加载?}
    B -->|否| C[不执行]
    B -->|是| D{地图实例是否存在?}
    D -->|是| E[更新地图中心点和缩放级别]
    D -->|否| F[获取位置坐标]
    F --> G[创建地图实例]
    G --> H[添加工具条和比例尺控件]
    H --> I{是否启用信息窗体?}
    I -->|是| J[创建信息窗体]
    I -->|否| K[不创建信息窗体]
    J --> L[保存地图实例]
    K --> L
    L --> M[设置mapReady状态]
    M --> N[调用地图创建完成回调]
```

### 3. 标记点管理

```mermaid
flowchart TD
    A[地图就绪] --> B[清除现有标记点]
    B --> C[清除聚合管理器]
    C --> D{是否有标记点?}
    D -->|否| E[结束]
    D -->|是| F[创建标记点数组]
    F --> G[遍历标记点]
    G --> H[设置标记点选项]
    H --> I{是否有自定义图标?}
    I -->|是| J[处理自定义图标]
    I -->|否| K[使用默认图标]
    J --> L{是否有自定义渲染?}
    K --> L
    L -->|是| M[使用自定义渲染]
    L -->|否| N{是否激活?}
    N -->|是| O[使用激活样式]
    N -->|否| P[使用默认样式]
    M --> Q[创建标记点]
    O --> Q
    P --> Q
    Q --> R[存储标记点引用]
    R --> S[添加点击事件]
    S --> T{是否启用聚合?}
    T -->|是| U[创建聚合管理器]
    T -->|否| V[直接添加到地图]
```

## 注意事项

1. 使用前需要配置高德地图 API 密钥和安全密钥，通过全局变量 `AMAP_CONFIG` 提供。
2. 组件默认使用深圳市的坐标作为默认位置，可以通过 `center` 或 `city` 属性指定其他位置。
3. 标记点聚合功能需要额外加载 `AMap.MarkerClusterer` 插件，可通过 `enableCluster` 属性启用。
4. 组件支持懒加载，当 `lazyLoading` 为 `true` 时，只有当地图容器在视图中时才会加载地图。
5. 自定义控件需要提供 React 节点作为内容，组件会将其渲染到地图上。

## 工具函数

组件提供了一些工具函数，可以在使用地图时调用：

- `lngLatToPixel`: 将经纬度坐标转换为像素坐标
- `pixelToLngLat`: 将像素坐标转换为经纬度坐标
- `calculateDistance`: 计算两点之间的距离（米）
- `getMarkerIconByStatus`: 根据标记点状态获取图标 URL
- `createCustomMarkerStyle`: 创建自定义图标样式
- `createClusterStyle`: 创建聚合点样式
- `getMarkersInView`: 获取地图视野范围内的标记点

## 贡献

欢迎提交问题和改进建议！

import {
  DollarOutlined,
  PercentageOutlined,
  RiseOutlined,
  ShoppingCartOutlined,
} from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { Card, Col, Row, Statistic } from 'antd';
import React from 'react';

interface StatisticsCardsProps {
  data?: API.RevenueOverviewStats;
  loading: boolean;
}

const StatisticsCards: React.FC<StatisticsCardsProps> = ({ data, loading }) => {
  return (
    <ProCard loading={loading} title="收入概览">
      {/* 收入流水明细 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24}>
          <Card
            title="📊 收入流水明细"
            size="small"
            style={{ backgroundColor: '#f6ffed' }}
          >
            <Row gutter={[16, 16]}>
              <Col xs={12} sm={8} md={6} lg={4} xl={4}>
                <Card
                  size="small"
                  style={{ height: '100%', textAlign: 'center' }}
                >
                  <Statistic
                    title="总原价"
                    value={data?.totalOriginalPrice || 0}
                    prefix={
                      <ShoppingCartOutlined style={{ color: '#722ed1' }} />
                    }
                    valueStyle={{ color: '#722ed1', fontSize: '16px' }}
                    precision={2}
                    suffix="元"
                  />
                  <div
                    style={{
                      fontSize: '12px',
                      color: '#666',
                      marginTop: '4px',
                    }}
                  >
                    所有订单原价
                  </div>
                </Card>
              </Col>
              <Col xs={12} sm={8} md={6} lg={4} xl={4}>
                <Card
                  size="small"
                  style={{ height: '100%', textAlign: 'center' }}
                >
                  <Statistic
                    title="优惠金额"
                    value={data?.totalDiscount || 0}
                    prefix={<PercentageOutlined style={{ color: '#faad14' }} />}
                    valueStyle={{ color: '#faad14', fontSize: '16px' }}
                    precision={2}
                    suffix="元"
                  />
                  <div
                    style={{
                      fontSize: '12px',
                      color: '#666',
                      marginTop: '4px',
                    }}
                  >
                    所有订单优惠
                  </div>
                </Card>
              </Col>
              <Col xs={12} sm={8} md={6} lg={4} xl={4}>
                <Card
                  size="small"
                  style={{ height: '100%', textAlign: 'center' }}
                >
                  <Statistic
                    title="实收金额"
                    value={data?.totalPaidAmount || 0}
                    prefix={<DollarOutlined style={{ color: '#13c2c2' }} />}
                    valueStyle={{ color: '#13c2c2', fontSize: '16px' }}
                    precision={2}
                    suffix="元"
                  />
                  <div
                    style={{
                      fontSize: '12px',
                      color: '#666',
                      marginTop: '4px',
                    }}
                  >
                    原价 - 优惠
                  </div>
                </Card>
              </Col>
              <Col xs={12} sm={8} md={6} lg={4} xl={4}>
                <Card
                  size="small"
                  style={{ height: '100%', textAlign: 'center' }}
                >
                  <Statistic
                    title="退款金额"
                    value={data?.totalRefundedAmount || 0}
                    prefix={<PercentageOutlined style={{ color: '#ff4d4f' }} />}
                    valueStyle={{ color: '#ff4d4f', fontSize: '16px' }}
                    precision={2}
                    suffix="元"
                  />
                  <div
                    style={{
                      fontSize: '12px',
                      color: '#666',
                      marginTop: '4px',
                    }}
                  >
                    退款订单金额
                  </div>
                </Card>
              </Col>
              <Col xs={24} sm={16} md={12} lg={8} xl={8}>
                <Card
                  size="small"
                  style={{
                    height: '100%',
                    textAlign: 'center',
                    border: '2px solid #1890ff',
                  }}
                >
                  <Statistic
                    title="净收入"
                    value={
                      (data?.totalPaidAmount || 0) -
                      (data?.totalRefundedAmount || 0)
                    }
                    prefix={<RiseOutlined style={{ color: '#1890ff' }} />}
                    valueStyle={{
                      color: '#1890ff',
                      fontSize: '16px',
                      fontWeight: 'bold',
                    }}
                    precision={2}
                    suffix="元"
                  />
                  <div
                    style={{
                      fontSize: '12px',
                      color: '#666',
                      marginTop: '4px',
                    }}
                  >
                    实收 - 退款
                  </div>
                </Card>
              </Col>
            </Row>
            <div
              style={{
                textAlign: 'center',
                marginTop: '16px',
                padding: '8px',
                backgroundColor: '#e6f7ff',
                borderRadius: '4px',
                fontSize: '14px',
                color: '#1890ff',
              }}
            >
              💡 收入流程：总原价 - 优惠金额 = 实收金额 → 实收金额 - 退款金额 =
              净收入
            </div>
          </Card>
        </Col>
      </Row>

      {/* 订单统计 */}
      {/* <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24}>
          <Card
            title="📋 订单统计"
            size="small"
            style={{ backgroundColor: '#fff7e6' }}
          >
            <Row gutter={[16, 16]}>
              <Col xs={12} sm={8} md={6} lg={6} xl={6}>
                <Card
                  size="small"
                  style={{ height: '100%', textAlign: 'center' }}
                >
                  <Statistic
                    title="总订单数"
                    value={data?.totalOrderCount || 0}
                    prefix={
                      <ShoppingCartOutlined style={{ color: '#1890ff' }} />
                    }
                    valueStyle={{ color: '#1890ff', fontSize: '16px' }}
                    suffix="单"
                  />
                </Card>
              </Col>
              <Col xs={12} sm={8} md={6} lg={6} xl={6}>
                <Card
                  size="small"
                  style={{ height: '100%', textAlign: 'center' }}
                >
                  <Statistic
                    title="有效订单数"
                    value={data?.effectiveOrderCount || 0}
                    prefix={
                      <ShoppingCartOutlined style={{ color: '#52c41a' }} />
                    }
                    valueStyle={{ color: '#52c41a', fontSize: '16px' }}
                    suffix="单"
                  />
                </Card>
              </Col>
              <Col xs={12} sm={8} md={6} lg={6} xl={6}>
                <Card
                  size="small"
                  style={{ height: '100%', textAlign: 'center' }}
                >
                  <Statistic
                    title="退款订单数"
                    value={data?.refundedOrderCount || 0}
                    prefix={
                      <ShoppingCartOutlined style={{ color: '#ff4d4f' }} />
                    }
                    valueStyle={{ color: '#ff4d4f', fontSize: '16px' }}
                    suffix="单"
                  />
                </Card>
              </Col>
              <Col xs={12} sm={8} md={6} lg={6} xl={6}>
                <Card
                  size="small"
                  style={{ height: '100%', textAlign: 'center' }}
                >
                  <Statistic
                    title="平均订单价值"
                    value={data?.avgOrderValue || 0}
                    prefix={<RiseOutlined style={{ color: '#722ed1' }} />}
                    valueStyle={{ color: '#722ed1', fontSize: '16px' }}
                    precision={2}
                    suffix="元"
                  />
                </Card>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row> */}

      {/* 主订单与追加服务对比 */}
      <Row gutter={[16, 16]}>
        <Col xs={24}>
          <Card
            title="🔄 业务类型对比"
            size="small"
            style={{ backgroundColor: '#f0f5ff' }}
          >
            <Row gutter={[16, 16]}>
              <Col xs={24} lg={12}>
                <Card
                  title={<span style={{ color: '#1890ff' }}>主订单统计</span>}
                  size="small"
                  style={{
                    border: '1px solid #1890ff',
                    backgroundColor: '#e6f7ff',
                  }}
                >
                  <Row gutter={[8, 8]}>
                    <Col span={12}>
                      <Statistic
                        title="订单数量"
                        value={data?.mainOrder?.orderCount || 0}
                        suffix="单"
                        valueStyle={{ fontSize: '14px', color: '#1890ff' }}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="总原价"
                        value={data?.mainOrder?.totalOriginalPrice || 0}
                        suffix="元"
                        precision={2}
                        valueStyle={{ fontSize: '14px', color: '#722ed1' }}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="实付金额"
                        value={data?.mainOrder?.paidAmount || 0}
                        suffix="元"
                        precision={2}
                        valueStyle={{ fontSize: '14px', color: '#13c2c2' }}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="退款金额"
                        value={data?.mainOrder?.refundedAmount || 0}
                        suffix="元"
                        precision={2}
                        valueStyle={{ fontSize: '14px', color: '#ff4d4f' }}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="净收入"
                        value={
                          (data?.mainOrder?.paidAmount || 0) -
                          (data?.mainOrder?.refundedAmount || 0)
                        }
                        suffix="元"
                        precision={2}
                        valueStyle={{
                          fontSize: '14px',
                          color: '#1890ff',
                          fontWeight: 'bold',
                        }}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="优惠金额"
                        value={data?.mainOrder?.totalDiscount || 0}
                        suffix="元"
                        precision={2}
                        valueStyle={{ fontSize: '14px', color: '#faad14' }}
                      />
                    </Col>
                  </Row>
                </Card>
              </Col>
              <Col xs={24} lg={12}>
                <Card
                  title={<span style={{ color: '#52c41a' }}>追加服务统计</span>}
                  size="small"
                  style={{
                    border: '1px solid #52c41a',
                    backgroundColor: '#f6ffed',
                  }}
                >
                  <Row gutter={[8, 8]}>
                    <Col span={12}>
                      <Statistic
                        title="订单数量"
                        value={data?.additionalService?.orderCount || 0}
                        suffix="单"
                        valueStyle={{ fontSize: '14px', color: '#52c41a' }}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="总原价"
                        value={data?.additionalService?.totalOriginalPrice || 0}
                        suffix="元"
                        precision={2}
                        valueStyle={{ fontSize: '14px', color: '#722ed1' }}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="实付金额"
                        value={data?.additionalService?.paidAmount || 0}
                        suffix="元"
                        precision={2}
                        valueStyle={{ fontSize: '14px', color: '#13c2c2' }}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="退款金额"
                        value={data?.additionalService?.refundedAmount || 0}
                        suffix="元"
                        precision={2}
                        valueStyle={{ fontSize: '14px', color: '#ff4d4f' }}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="净收入"
                        value={
                          (data?.additionalService?.paidAmount || 0) -
                          (data?.additionalService?.refundedAmount || 0)
                        }
                        suffix="元"
                        precision={2}
                        valueStyle={{
                          fontSize: '14px',
                          color: '#52c41a',
                          fontWeight: 'bold',
                        }}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="优惠金额"
                        value={data?.additionalService?.totalDiscount || 0}
                        suffix="元"
                        precision={2}
                        valueStyle={{ fontSize: '14px', color: '#faad14' }}
                      />
                    </Col>
                  </Row>
                </Card>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </ProCard>
  );
};

export default StatisticsCards;

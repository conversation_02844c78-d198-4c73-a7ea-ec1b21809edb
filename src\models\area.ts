import { area } from '@/services';
import { convertListToTree } from '@/utils/calc';
import { message } from 'antd';

export interface TreeNode extends API.Area {
  label: string;
  value: string;
  title: string;
  key: string;
  children?: TreeNode[];
  [key: string]: any;
}

export type AreaState = {
  list: API.Area[];
  treeData: TreeNode[];
  inited: boolean;
};
export default {
  state: {
    list: [],
    treeData: [],
    inited: false,
  } as AreaState,

  effects: {
    /** 查询列表 **/
    *queryList(
      { payload }: { payload: Record<string, any> },
      { call, put }: { call: Call; put: Put },
    ) {
      const { errCode, msg, data } = yield call(area.index, payload);
      if (errCode) {
        message.error(msg || '区域列表查询失败');
      } else {
        yield put({ type: 'querySuccess', payload: data.list || [] });
      }
    },
    *add(
      { payload }: { payload: API.Area },
      { call, put }: { call: Call; put: Put },
    ) {
      const { errCode, msg, data } = yield call(area.create, payload);
      if (errCode) {
        message.error(msg || '区域创建失败');
      } else {
        message.success('区域创建成功');
        yield put({ type: 'addSuccess', payload: data });
      }
    },
    *update(
      { payload }: { payload: { code: string; info: Partial<API.Area> } },
      { call, put }: { call: Call; put: Put },
    ) {
      const { errCode, msg } = yield call(
        area.update,
        payload.code,
        payload.info,
      );
      if (errCode) {
        message.error(msg || '区域更新失败');
      } else {
        message.success('区域更新成功');
        yield put({ type: 'updateSuccess', payload });
      }
    },
    *remove(
      { payload }: { payload: { code: string } },
      { call, put }: { call: Call; put: Put },
    ) {
      const { errCode, msg } = yield call(area.remove, payload.code);
      if (errCode) {
        message.error(msg || '区域删除失败');
      } else {
        message.success('区域删除成功');
        yield put({ type: 'removeSuccess', payload });
      }
    },
  },

  reducers: {
    /** 查询列表成功后更新数据，触发渲染 */
    querySuccess(_state: AreaState, { payload }: { payload: API.Area[] }) {
      const list = payload.map((item: API.Area) => ({
        label: item.name,
        value: item.code,
        title: item.name,
        key: item.code,
        ...item,
      }));
      // 封装树型结构
      const treeData = convertListToTree({
        list,
        parentKey: '',
        childKey: 'children',
        fieldName: {
          parent: 'code',
          child: 'parentCode',
        },
      });
      return {
        list: payload,
        treeData,
        inited: true,
      };
    },
    addSuccess(state: AreaState, { payload }: { payload: API.Area }) {
      const newList = [...state.list, payload].sort((a, b) =>
        a.code.localeCompare(b.code),
      );
      const list = newList.map((item: API.Area) => ({
        label: item.name,
        value: item.code,
        title: item.name,
        key: item.code,
        ...item,
      }));
      // 封装树型结构
      const treeData = convertListToTree({
        list,
        parentKey: '',
        childKey: 'children',
        fieldName: {
          parent: 'code',
          child: 'parentCode',
        },
      });
      return {
        list: newList,
        treeData,
      };
    },
    updateSuccess(
      state: AreaState,
      { payload }: { payload: { code: string; info: Partial<API.Area> } },
    ) {
      const newList = state.list
        .map((item) => {
          if (item.code === payload.code) {
            return { ...item, ...payload.info };
          }
          return item;
        })
        .sort((a, b) => a.code.localeCompare(b.code));
      const list = newList.map((item: API.Area) => ({
        label: item.name,
        value: item.code,
        title: item.name,
        key: item.code,
        ...item,
      }));
      // 封装树型结构
      const treeData = convertListToTree({
        list,
        parentKey: '',
        childKey: 'children',
        fieldName: {
          parent: 'code',
          child: 'parentCode',
        },
      });
      return {
        list: newList,
        treeData,
      };
    },
    removeSuccess(
      state: AreaState,
      { payload }: { payload: { code: string } },
    ) {
      const newList = state.list.filter((item) => item.code !== payload.code);
      const list = newList.map((item: API.Area) => ({
        label: item.name,
        value: item.code,
        title: item.name,
        key: item.code,
        ...item,
      }));
      // 封装树型结构
      const treeData = convertListToTree({
        list,
        parentKey: '',
        childKey: 'children',
        fieldName: {
          parent: 'code',
          child: 'parentCode',
        },
      });
      return {
        list: newList,
        treeData,
      };
    },
  },
};

/*
 * @Description: 多图片上传组件，在ProForm中使用
 * @Date: 2025-01-09
 */
import { deleteFile as delete_cos, upload as upload_cos } from '@/utils/cos';
import { deleteFile as delete_zos, upload as upload_zos } from '@/utils/zos';
import {
  ProFormUploadButton,
  ProFormUploadButtonProps,
} from '@ant-design/pro-components';
import { message, Modal, Upload, UploadProps } from 'antd';
import React from 'react';

type ProFormImgMultipleProps = ProFormUploadButtonProps & {
  /** 大小限制，默认2M */
  maxSize?: {
    size: number;
    message: string;
  };
  /** 上传路径前缀，默认images */
  dir?: string;
  /** 最大上传数量，默认6 */
  maxCount?: number;
};

const ProFormImgMultiple: React.FC<ProFormImgMultipleProps> = ({
  maxSize = {
    size: 2 * 1024 * 1024,
    message: '图片大小不能超过2M',
  },
  dir = 'images',
  maxCount = 6,
  ...uploadProps
}) => {
  const customRequest: UploadProps['customRequest'] = async (options) => {
    if (!options.file) {
      message.warning('没有读取到文件，请重新上传！');
      return;
    }

    const file = options.file as File;
    /** 云对象存储上传 */
    const upload = uploadType === 'cos' ? upload_cos : upload_zos;
    await upload({
      file,
      dir,
      name: `${Date.now()}_${file.name}`,
      onProgress: (progressData) => {
        options.onProgress?.({ percent: progressData.percent * 100 });
      },
      onOK: (url) => {
        console.log('Upload success, URL:', url);
        // 在多图片模式下，需要传递包含response的对象
        options.onSuccess?.({
          url: url,
          response: url,
          status: 'done',
        });
      },
      onError: (err) => {
        console.log('Upload error:', err);
        options.onError?.(err);
      },
    });
  };

  return (
    <ProFormUploadButton
      {...uploadProps}
      max={maxCount}
      listType="picture-card"
      fieldProps={{
        multiple: true,
        accept: 'image/*',
        onPreview: (file) => {
          const url = file.thumbUrl || file.url || file.response;
          if (url) {
            Modal.info({
              title: '预览',
              content: (
                <img
                  src={url}
                  alt="预览"
                  style={{ maxWidth: '100%', maxHeight: '100%' }}
                />
              ),
            });
          }
        },
        beforeUpload: (file) => {
          const isImage = file.type.startsWith('image/');
          if (!isImage) {
            message.error(`${file.name} 不是一个图片文件`);
            return Upload.LIST_IGNORE;
          }
          if (file.size > maxSize.size) {
            message.error(maxSize.message);
            return Upload.LIST_IGNORE;
          }
          return true;
        },
        customRequest,
        onRemove: (file) => {
          if (file.status === 'done') {
            const deleteFile = uploadType === 'cos' ? delete_cos : delete_zos;
            deleteFile({ url: file.response as string });
          }
          return true;
        },
      }}
      transform={(value, namePath) => {
        console.log('value', value);
        let urlArray: string[] = [];
        // 提交时：将文件对象数组转换为URL字符串数组
        if (Array.isArray(value) && value.length > 0) {
          urlArray = value
            .map((item) => {
              // 如果已经是字符串URL，直接返回
              if (typeof item === 'string') {
                return item;
              }
              // 如果是文件对象，提取URL
              if (item && item.response) {
                return item.response.url.startsWith('//')
                  ? item.response.url
                  : '//' + item.response.url;
              }
              return '';
            })
            .filter(Boolean);
        }
        console.log('urlArray', urlArray);
        return { [namePath]: urlArray };
      }}
      // convertValue={(value) => {
      //   // 回填时：将URL字符串数组转换为文件对象数组
      //   if (Array.isArray(value) && value.length > 0) {
      //     // 如果是字符串数组（URL），转换为文件对象
      //     if (typeof value[0] === 'string') {
      //       return value.map((url, index) => ({
      //         uid: `existing-${index}`,
      //         name: `image-${index + 1}`,
      //         status: 'done',
      //         url: url,
      //         response: url,
      //         thumbUrl: url,
      //       }));
      //     }
      //   }
      //   // 无图片或空数组情况
      //   return [];
      // }}
    />
  );
};

export default ProFormImgMultiple;

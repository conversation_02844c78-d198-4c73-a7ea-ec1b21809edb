.order-amount-anomaly {
  // 页面头部内容样式
  .ant-page-header-content {
    .ant-typography p {
      margin-bottom: 8px;
      color: #666;
    }
  }

  // 步骤指引样式
  .ant-steps {
    .ant-steps-item-title {
      font-size: 14px !important;
    }

    .ant-steps-item-description {
      font-size: 12px;
      color: #999;
    }
  }

  // 统计卡片样式
  .stats-card {
    .ant-statistic-content {
      font-size: 18px;
    }

    .ant-progress {
      margin-top: 8px;
    }

    .ant-card-head-title {
      display: flex;
      align-items: center;
    }
  }

  // 操作面板样式
  .operation-panel {
    .ant-divider {
      margin: 24px 0 16px;
    }

    .ant-typography h5 {
      margin-bottom: 8px;
      display: flex;
      align-items: center;
    }

    .ant-typography .ant-typography {
      margin-bottom: 16px;
      font-size: 13px;
      line-height: 1.5;
    }

    // 按钮组样式
    .ant-row {
      .ant-col {
        margin-bottom: 8px;
      }
    }

    // Tooltip 样式
    .ant-tooltip {
      max-width: 300px;
    }
  }

  // 结果展示样式
  .result-display {
    .ant-card-head-title {
      display: flex;
      align-items: center;
    }

    .ant-table-small {
      .ant-table-thead > tr > th {
        background: #fafafa;
        font-weight: 600;
      }
    }

    .ant-descriptions-item-label {
      font-weight: 600;
      color: #666;
    }

    // 结果卡片间距
    .ant-card {
      margin-bottom: 16px;
    }
  }

  // 提示信息样式
  .maintenance-tips {
    background: #f6ffed;
    border: 1px solid #b7eb8f;
    border-radius: 6px;
    padding: 12px;
    margin-top: 16px;

    .anticon {
      color: #52c41a;
      margin-right: 8px;
    }
  }

  .warning-tips {
    background: #fff7e6;
    border: 1px solid #ffd591;
    border-radius: 6px;
    padding: 12px;
    margin-top: 16px;

    .anticon {
      color: #fa8c16;
      margin-right: 8px;
    }
  }

  .error-tips {
    background: #fff2f0;
    border: 1px solid #ffccc7;
    border-radius: 6px;
    padding: 12px;
    margin-top: 16px;

    .anticon {
      color: #ff4d4f;
      margin-right: 8px;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .ant-steps {
      .ant-steps-item-title {
        font-size: 12px !important;
      }

      .ant-steps-item-description {
        font-size: 11px;
      }
    }

    .operation-panel {
      .ant-row {
        .ant-col {
          width: 100%;
          margin-bottom: 12px;
        }
      }
    }
  }
}

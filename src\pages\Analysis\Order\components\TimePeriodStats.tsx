import * as order from '@/services/order';
import { ProCard } from '@ant-design/pro-components';
import { Empty, List, message, Progress, Select, Tag } from 'antd';
import { Dayjs } from 'dayjs';
import React, { useEffect, useState } from 'react';

interface TimePeriodStatsProps {
  dateRange: [Dayjs, Dayjs];
}

interface TimePeriodData {
  period: number;
  periodLabel: string;
  orderCount: number;
  totalAmount: number;
  avgAmount: number;
}

const TimePeriodStats: React.FC<TimePeriodStatsProps> = ({ dateRange }) => {
  const [timePeriodData, setTimePeriodData] = useState<TimePeriodData[]>([]);
  const [loading, setLoading] = useState(false);
  const [periodType, setPeriodType] = useState<'hour' | 'weekday'>('hour');

  // 获取时段统计数据
  const fetchTimePeriodData = async () => {
    setLoading(true);
    try {
      const { errCode, msg, data } = await order.timePeriodStats({
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD'),
        periodType,
      });

      if (errCode) {
        message.error(msg || '获取时段统计失败');
        return;
      }

      setTimePeriodData(data || []);
    } catch (error) {
      console.error('获取时段统计失败:', error);
      message.error('获取时段统计失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTimePeriodData();
  }, [dateRange, periodType]);

  // 渲染时段统计
  const renderTimePeriodStats = () => {
    if (timePeriodData.length === 0) {
      return <Empty description="暂无数据" />;
    }

    const maxOrderCount = Math.max(
      ...timePeriodData.map((item) => item.orderCount),
    );

    return (
      <List
        dataSource={timePeriodData}
        renderItem={(item) => {
          const percent =
            maxOrderCount > 0 ? (item.orderCount / maxOrderCount) * 100 : 0;

          // 根据订单数量设置不同的标签颜色
          let tagColor = 'default';
          if (item.orderCount >= maxOrderCount * 0.8) tagColor = 'red';
          else if (item.orderCount >= maxOrderCount * 0.6) tagColor = 'orange';
          else if (item.orderCount >= maxOrderCount * 0.4) tagColor = 'blue';
          else if (item.orderCount >= maxOrderCount * 0.2) tagColor = 'green';

          return (
            <List.Item style={{ padding: '12px 0' }}>
              <div style={{ width: '100%' }}>
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginBottom: 8,
                  }}
                >
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <span
                      style={{
                        fontWeight: 500,
                        fontSize: '14px',
                        marginRight: 8,
                      }}
                    >
                      {item.periodLabel}
                    </span>
                    <Tag color={tagColor}>{item.orderCount} 单</Tag>
                  </div>
                  <span style={{ fontSize: '12px', color: '#999' }}>
                    ¥{item.totalAmount.toFixed(2)}
                  </span>
                </div>

                <div style={{ marginBottom: 4 }}>
                  <Progress
                    percent={percent}
                    strokeColor={
                      tagColor === 'red'
                        ? '#f5222d'
                        : tagColor === 'orange'
                        ? '#fa8c16'
                        : tagColor === 'blue'
                        ? '#1890ff'
                        : tagColor === 'green'
                        ? '#52c41a'
                        : '#d9d9d9'
                    }
                    showInfo={false}
                    size="small"
                  />
                </div>

                <div
                  style={{
                    fontSize: '12px',
                    color: '#666',
                    textAlign: 'right',
                  }}
                >
                  平均: ¥{item.avgAmount.toFixed(2)}
                </div>
              </div>
            </List.Item>
          );
        }}
      />
    );
  };

  return (
    <ProCard
      title="时段订单分布"
      loading={loading}
      style={{ height: '400px', overflow: 'auto' }}
      extra={
        <Select
          value={periodType}
          onChange={setPeriodType}
          style={{ width: 100 }}
          size="small"
        >
          <Select.Option value="hour">按小时</Select.Option>
          <Select.Option value="weekday">按星期</Select.Option>
        </Select>
      }
    >
      {renderTimePeriodStats()}
    </ProCard>
  );
};

export default TimePeriodStats;

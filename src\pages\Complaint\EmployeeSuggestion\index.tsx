import {
  ComplaintStatus,
  ComplaintSubCategory,
  StatusConfig,
  SubCategoryConfig,
} from '@/constants/complaint';
import { complaints } from '@/services';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import {
  Button,
  message,
  Popconfirm,
  Space,
  Tag,
  Tooltip,
  Typography,
} from 'antd';
import React, { useRef, useState } from 'react';
import ComplaintDetailDrawer from '../components/ComplaintDetailDrawer';
import ComplaintHandleModal from '../components/ComplaintHandleModal';
import ComplaintHistoryModal from '../components/ComplaintHistoryModal';
import ProcessGuideModal from '../components/ProcessGuideModal';

const { Text } = Typography;

const EmployeeSuggestionManagement: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [detailVisible, setDetailVisible] = useState(false);
  const [handleVisible, setHandleVisible] = useState(false);
  const [historyVisible, setHistoryVisible] = useState(false);
  const [processGuideVisible, setProcessGuideVisible] = useState(false);
  const [currentSuggestion, setCurrentSuggestion] = useState<API.Complaint>();

  /** 查看详情 */
  const handleViewDetail = (record: API.Complaint) => {
    setCurrentSuggestion(record);
    setDetailVisible(true);
  };

  /** 处理建议 */
  const handleProcess = (record: API.Complaint) => {
    setCurrentSuggestion(record);
    setHandleVisible(true);
  };

  /** 查看处理流程 */
  const handleViewHistory = (record: API.Complaint) => {
    setCurrentSuggestion(record);
    setHistoryVisible(true);
  };

  /** 删除建议 */
  const handleDelete = async (record: API.Complaint) => {
    const response = await complaints.remove(record.id);
    if (response.errCode) {
      message.error(response.msg || '删除失败');
    } else {
      message.success('删除成功');
      actionRef.current?.reload();
    }
  };

  /** 处理建议提交 */
  const handleProcessSubmit = async (values: {
    status: API.ComplaintStatus;
    result?: string;
    handlerId: number;
  }) => {
    if (!currentSuggestion) return;

    const response = await complaints.handle(currentSuggestion.id, values);
    if (response.errCode) {
      message.error(response.msg || '处理失败');
    } else {
      message.success('处理成功');
      setHandleVisible(false);
      actionRef.current?.reload();
    }
  };

  /** 获取状态标签 */
  const getStatusTag = (status: API.ComplaintStatus) => {
    const config = StatusConfig[status];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const columns: ProColumns<API.Complaint>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 80,
      search: false,
    },
    {
      title: '标题',
      dataIndex: 'title',
      width: 200,
      ellipsis: true,
      render: (text, record) => (
        <Button type="link" onClick={() => handleViewDetail(record)}>
          {text}
        </Button>
      ),
    },
    {
      title: '建议类型',
      dataIndex: 'subCategory',
      width: 120,
      valueType: 'select',
      valueEnum: {
        [ComplaintSubCategory.平台建议]: { text: '平台建议' },
        [ComplaintSubCategory.服务建议]: { text: '服务建议' },
        [ComplaintSubCategory.流程投诉]: { text: '流程建议' },
      },
      render: (_, record) => (
        <Tag color="blue">{SubCategoryConfig[record.subCategory]}</Tag>
      ),
    },
    {
      title: '提交员工',
      dataIndex: ['employee', 'name'],
      width: 120,
      search: false,
      render: (text, record) => (
        <Space direction="vertical" size={0}>
          <Text>{text || '未知员工'}</Text>
          <Text type="secondary" style={{ fontSize: 12 }}>
            ID: {record.employeeId}
          </Text>
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      valueType: 'select',
      valueEnum: {
        [ComplaintStatus.待处理]: { text: '待处理' },
        [ComplaintStatus.处理中]: { text: '处理中' },
        [ComplaintStatus.已解决]: { text: '已解决' },
        [ComplaintStatus.已关闭]: { text: '已关闭' },
      },
      render: (_, record) => getStatusTag(record.status),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 160,
      valueType: 'dateTime',
      search: false,
    },
    {
      title: '操作',
      valueType: 'option',
      width: 280,
      render: (_, record) =>
        [
          <Button
            key="detail"
            type="link"
            size="small"
            onClick={() => handleViewDetail(record)}
          >
            查看详情
          </Button>,
          <Button
            key="history"
            type="link"
            size="small"
            onClick={() => handleViewHistory(record)}
          >
            处理流程
          </Button>,
          record.status === ComplaintStatus.待处理 ||
          record.status === ComplaintStatus.处理中 ? (
            <Button
              key="handle"
              type="link"
              size="small"
              onClick={() => handleProcess(record)}
            >
              处理
            </Button>
          ) : null,
          record.status === ComplaintStatus.待处理 ? (
            <Popconfirm
              key="delete"
              title="确定要删除这条员工建议吗？"
              onConfirm={() => handleDelete(record)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" size="small" danger>
                删除
              </Button>
            </Popconfirm>
          ) : null,
        ].filter(Boolean),
    },
  ];

  return (
    <>
      <ProTable<API.Complaint>
        actionRef={actionRef}
        rowKey="id"
        headerTitle={
          <Space>
            员工建议管理
            <Tooltip title="点击查看处理流程说明">
              <QuestionCircleOutlined
                style={{ color: '#1890ff', cursor: 'pointer' }}
                onClick={() => setProcessGuideVisible(true)}
              />
            </Tooltip>
          </Space>
        }
        columns={columns}
        search={{
          labelWidth: 'auto',
        }}
        pagination={{
          pageSize: 20,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
        }}
        scroll={{ x: 1200 }}
        request={async (params, sort) => {
          const { errCode, msg, data } = await complaints.employeeSuggestions({
            ...params,
            ...sort,
          });
          if (errCode) {
            message.error(msg || '列表查询失败');
            return {
              data: [],
              total: 0,
              success: false,
            };
          }
          return {
            data: data?.list || [],
            total: data?.total || 0,
            success: true,
          };
        }}
        toolBarRender={() => [
          <Button key="refresh" onClick={() => actionRef.current?.reload()}>
            刷新
          </Button>,
        ]}
      />

      {/* 详情抽屉 */}
      <ComplaintDetailDrawer
        visible={detailVisible}
        complaint={currentSuggestion}
        onClose={() => {
          setDetailVisible(false);
          setCurrentSuggestion(undefined);
        }}
      />

      {/* 处理模态框 */}
      <ComplaintHandleModal
        visible={handleVisible}
        complaint={currentSuggestion}
        onSubmit={handleProcessSubmit}
        onClose={() => {
          setHandleVisible(false);
          setCurrentSuggestion(undefined);
        }}
      />

      {/* 流程说明模态框 */}
      <ProcessGuideModal
        visible={processGuideVisible}
        onClose={() => setProcessGuideVisible(false)}
      />

      {/* 处理历史模态框 */}
      <ComplaintHistoryModal
        visible={historyVisible}
        complaintId={currentSuggestion?.id}
        onClose={() => {
          setHistoryVisible(false);
          setCurrentSuggestion(undefined);
        }}
      />
    </>
  );
};

export default EmployeeSuggestionManagement;

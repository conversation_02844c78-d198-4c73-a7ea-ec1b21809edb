import { request } from '@umijs/max';

/** 查询照片墙列表  GET /photo-walls */
export async function index(params: Record<string, any>) {
  return request<API.ResType<{ total?: number; list?: API.PhotoWall[] }>>(
    '/photo-walls',
    {
      method: 'GET',
      params,
    },
  );
}

/** 新增照片墙记录  POST /photo-walls */
export async function create(
  body: Omit<
    API.PhotoWall,
    'id' | 'isEnabled' | 'likeCount' | 'viewCount' | 'createdAt' | 'updatedAt'
  >,
) {
  return request<API.ResType<API.PhotoWall>>('/photo-walls', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 更新照片墙记录  PUT /photo-walls/:id */
export async function update(id: number, body: Partial<API.PhotoWall>) {
  return request<API.ResType<boolean>>(`/photo-walls/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 删除照片墙记录  DELETE /photo-walls/:id */
export async function remove(id: number) {
  return request<API.ResType<boolean>>(`/photo-walls/${id}`, {
    method: 'DELETE',
  });
}

/** 启用/禁用照片展示  PUT /photo-walls/:id/toggle-enabled */
export async function toggleEnabled(id: number, isEnabled: boolean) {
  return request<API.ResType<{ id: number; isEnabled: boolean }>>(
    `/photo-walls/${id}/toggle-enabled`,
    {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      data: { isEnabled },
    },
  );
}

/** 设置展示优先级  PUT /photo-walls/:id/priority */
export async function setPriority(id: number, priority: number) {
  return request<API.ResType<{ id: number; priority: number }>>(
    `/photo-walls/${id}/priority`,
    {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      data: { priority },
    },
  );
}

/** 获取照片墙统计信息  GET /photo-walls/statistics */
export async function getStatistics() {
  return request<API.ResType<API.PhotoWallStatistics>>(
    '/photo-walls/statistics',
    {
      method: 'GET',
    },
  );
}

/** 获取可用服务照片素材  GET /photo-walls/available-service-photos */
export async function getAvailableServicePhotos(params: Record<string, any>) {
  return request<API.ResType<{ total?: number; list?: API.ServicePhoto[] }>>(
    '/photo-walls/available-service-photos',
    {
      method: 'GET',
      params,
    },
  );
}

/** 从选择的服务照片创建照片墙  POST /photo-walls/create-from-selected */
export async function createFromSelected(body: {
  servicePhotoId: number;
  selectedBeforePhoto: string;
  selectedAfterPhoto: string;
  title?: string;
  description?: string;
  priority?: number;
}) {
  return request<API.ResType<API.PhotoWall>>(
    '/photo-walls/create-from-selected',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
    },
  );
}

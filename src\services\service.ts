import { request } from '@umijs/max';

/** 查询列表  GET /service */
export async function index(params: Record<string, any>) {
  return request<API.ResType<{ total?: number; list?: API.Service[] }>>(
    '/service',
    {
      method: 'GET',
      params,
    },
  );
}

/** 创建  POST /service */
export async function create(body: Omit<API.Service, 'id'>) {
  return request<API.ResType<API.Service>>('/service', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 按ID查询  GET /service/:id */
export async function show(id: number) {
  return request<API.ResType<API.Service>>(`/service/${id}`, {
    method: 'GET',
  });
}

/** 修改  PUT /service/:id */
export async function update(id: number, body: Partial<API.Service>) {
  return request<API.ResType<unknown>>(`/service/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 删除  DELETE /service/:id */
export async function remove(id: number) {
  return request<API.ResType<unknown>>(`/service/${id}`, {
    method: 'DELETE',
  });
}

export async function setAdditionalService(
  id: number,
  body: { additional_service_ids: number[] },
) {
  return request<API.ResType<unknown>>(`/service/${id}/additional-service`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

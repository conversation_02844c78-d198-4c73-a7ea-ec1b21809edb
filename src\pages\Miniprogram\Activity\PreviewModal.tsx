import { Modal } from 'antd';
import React from 'react';

type PreviewModalProps = {
  open: boolean;
  activity?: API.Activity;
  onClose: () => void;
};

const PreviewModal: React.FC<PreviewModalProps> = ({
  open,
  activity,
  onClose,
}) => {
  if (!activity) return null;

  return (
    <Modal
      title={`预览活动 - ${activity.title}`}
      open={open}
      onCancel={onClose}
      footer={null}
      width="375px"
      style={{ top: 20 }}
      bodyStyle={{ padding: 0, height: '667px', overflow: 'hidden' }}
    >
      <div
        style={{
          width: '100%',
          height: '100%',
          border: '1px solid #f0f0f0',
          borderRadius: '8px',
          overflow: 'hidden',
          backgroundColor: '#fff',
        }}
      >
        {activity.contentType === 'content' ? (
          <div
            style={{
              padding: '16px',
              height: '100%',
              overflow: 'auto',
              fontSize: '14px',
              lineHeight: '1.6',
              color: '#333',
            }}
          >
            {/* 活动标题 */}
            <div
              style={{
                fontSize: '18px',
                fontWeight: 'bold',
                marginBottom: '16px',
                textAlign: 'center',
                borderBottom: '1px solid #f0f0f0',
                paddingBottom: '12px',
              }}
            >
              {activity.title}
            </div>

            {/* 封面图片 */}
            {/* {activity.coverImage && (
              <div style={{ marginBottom: '16px', textAlign: 'center' }}>
                <img 
                  src={activity.coverImage} 
                  alt="活动封面"
                  style={{ 
                    maxWidth: '100%', 
                    height: 'auto',
                    borderRadius: '4px'
                  }}
                />
              </div>
            )} */}

            {/* 富文本内容 */}
            <div
              dangerouslySetInnerHTML={{ __html: activity.content || '' }}
              style={{
                wordBreak: 'break-word',
              }}
            />
          </div>
        ) : (
          <div style={{ height: '100%', position: 'relative' }}>
            {/* 活动标题栏 */}
            <div
              style={{
                padding: '12px 16px',
                backgroundColor: '#f8f9fa',
                borderBottom: '1px solid #e9ecef',
                fontSize: '16px',
                fontWeight: 'bold',
                textAlign: 'center',
              }}
            >
              {activity.title}
            </div>

            {/* iframe内容 */}
            <iframe
              src={activity.url}
              style={{
                width: '100%',
                height: 'calc(100% - 49px)',
                border: 'none',
              }}
              title="活动预览"
              sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
            />
          </div>
        )}
      </div>
    </Modal>
  );
};

export default PreviewModal;

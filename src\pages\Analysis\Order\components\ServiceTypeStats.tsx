import * as order from '@/services/order';
import { ProCard } from '@ant-design/pro-components';
import { Empty, List, message, Progress } from 'antd';
import { Dayjs } from 'dayjs';
import React, { useEffect, useState } from 'react';

interface ServiceTypeStatsProps {
  dateRange: [Dayjs, Dayjs];
}

interface ServiceTypeData {
  serviceTypeId: number;
  serviceTypeName: string;
  serviceTypeCode: string;
  orderCount: number;
  totalAmount: number;
}

const ServiceTypeStats: React.FC<ServiceTypeStatsProps> = ({ dateRange }) => {
  const [serviceTypeData, setServiceTypeData] = useState<ServiceTypeData[]>([]);
  const [loading, setLoading] = useState(false);

  // 获取服务类型统计数据
  const fetchServiceTypeData = async () => {
    setLoading(true);
    try {
      const { errCode, msg, data } = await order.serviceTypeStats({
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD'),
      });

      if (errCode) {
        message.error(msg || '获取服务类型统计失败');
        return;
      }

      setServiceTypeData(data || []);
    } catch (error) {
      console.error('获取服务类型统计失败:', error);
      message.error('获取服务类型统计失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchServiceTypeData();
  }, [dateRange]);

  // 渲染服务类型统计
  const renderServiceTypeStats = () => {
    if (serviceTypeData.length === 0) {
      return <Empty description="暂无数据" />;
    }

    const maxOrderCount = Math.max(
      ...serviceTypeData.map((item) => item.orderCount),
    );
    const maxAmount = Math.max(
      ...serviceTypeData.map((item) => item.totalAmount),
    );

    return (
      <List
        dataSource={serviceTypeData}
        renderItem={(item, index) => {
          const orderPercent =
            maxOrderCount > 0 ? (item.orderCount / maxOrderCount) * 100 : 0;
          const amountPercent =
            maxAmount > 0 ? (item.totalAmount / maxAmount) * 100 : 0;

          const colors = [
            '#1890ff',
            '#52c41a',
            '#faad14',
            '#f5222d',
            '#722ed1',
            '#13c2c2',
          ];
          const color = colors[index % colors.length];

          return (
            <List.Item style={{ padding: '12px 0' }}>
              <div style={{ width: '100%' }}>
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginBottom: 8,
                  }}
                >
                  <span style={{ fontWeight: 500, fontSize: '14px' }}>
                    {item.serviceTypeName}
                  </span>
                  <span style={{ fontSize: '12px', color: '#999' }}>
                    {item.orderCount} 单
                  </span>
                </div>

                <div style={{ marginBottom: 4 }}>
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      fontSize: '12px',
                      color: '#666',
                      marginBottom: 2,
                    }}
                  >
                    <span>订单数量</span>
                    <span>{item.orderCount} 单</span>
                  </div>
                  <Progress
                    percent={orderPercent}
                    strokeColor={color}
                    showInfo={false}
                    size="small"
                  />
                </div>

                <div>
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      fontSize: '12px',
                      color: '#666',
                      marginBottom: 2,
                    }}
                  >
                    <span>订单金额</span>
                    <span>¥{item.totalAmount.toFixed(2)}</span>
                  </div>
                  <Progress
                    percent={amountPercent}
                    strokeColor={color}
                    showInfo={false}
                    size="small"
                  />
                </div>
              </div>
            </List.Item>
          );
        }}
      />
    );
  };

  return (
    <ProCard
      title="服务类型统计"
      loading={loading}
      style={{ height: '300px', overflow: 'auto' }}
    >
      {renderServiceTypeStats()}
    </ProCard>
  );
};

export default ServiceTypeStats;

import * as complaints from '@/services/complaints';
import { Column } from '@ant-design/charts';
import { ProCard } from '@ant-design/pro-components';
import { Card, Col, message, Row, Statistic } from 'antd';
import { Dayjs } from 'dayjs';
import React, { useEffect, useState } from 'react';

interface ProcessingEfficiencyProps {
  dateRange: [Dayjs, Dayjs];
}

// 子分类标签映射
const getSubCategoryLabel = (subCategory: string) => {
  const subCategoryMap: Record<string, string> = {
    order: '订单投诉',
    employee: '人员投诉',
    platform: '平台建议',
    service: '服务建议',
  };
  return subCategoryMap[subCategory] || subCategory;
};

const ProcessingEfficiency: React.FC<ProcessingEfficiencyProps> = ({
  dateRange,
}) => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<API.ComplaintProcessingEfficiencyStats>({
    averageProcessingHours: 0,
    fastProcessingCount: 0,
    slowProcessingCount: 0,
    fastProcessingRate: 0,
    slowProcessingRate: 0,
    categoryEfficiency: [],
    subCategoryEfficiency: [],
  });

  const fetchData = async () => {
    setLoading(true);
    try {
      const {
        errCode,
        msg,
        data: responseData,
      } = await complaints.processingEfficiency({
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD'),
      });

      if (errCode) {
        message.error(msg || '获取处理效率数据失败');
        return;
      }

      if (responseData) {
        setData(responseData);
      }
    } catch (error) {
      console.error('获取处理效率数据失败:', error);
      message.error('获取处理效率数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [dateRange]);

  // 转换数据格式用于图表
  const chartData = data.subCategoryEfficiency.map((item) => ({
    category: getSubCategoryLabel(item.subCategory),
    averageHours: item.averageHours,
    count: item.count,
  }));

  const config = {
    data: chartData,
    xField: 'category',
    yField: 'averageHours',
    label: {
      position: 'middle' as const,
      style: {
        fill: '#FFFFFF',
        opacity: 0.6,
      },
    },
    xAxis: {
      label: {
        autoHide: true,
        autoRotate: false,
      },
    },
    meta: {
      category: {
        alias: '类型',
      },
      averageHours: {
        alias: '平均处理时间(小时)',
      },
    },
    tooltip: {
      formatter: (datum: any) => {
        return {
          name: '平均处理时间',
          value: `${datum.averageHours.toFixed(1)} 小时 (${datum.count} 件)`,
        };
      },
    },
  };

  return (
    <ProCard title="处理效率分析" loading={loading}>
      {/* 效率统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col xs={12} sm={6}>
          <Card size="small" style={{ textAlign: 'center' }}>
            <Statistic
              title="平均处理时间"
              value={data.averageProcessingHours}
              precision={1}
              suffix="小时"
              valueStyle={{ color: '#1890ff', fontSize: '16px' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card size="small" style={{ textAlign: 'center' }}>
            <Statistic
              title="快速处理率"
              value={data.fastProcessingRate}
              precision={1}
              suffix="%"
              valueStyle={{ color: '#52c41a', fontSize: '16px' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card size="small" style={{ textAlign: 'center' }}>
            <Statistic
              title="快速处理数量"
              value={data.fastProcessingCount}
              suffix="件"
              valueStyle={{ color: '#52c41a', fontSize: '16px' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={6}>
          <Card size="small" style={{ textAlign: 'center' }}>
            <Statistic
              title="慢速处理数量"
              value={data.slowProcessingCount}
              suffix="件"
              valueStyle={{ color: '#ff4d4f', fontSize: '16px' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 各类型处理效率图表 */}
      <Column {...config} height={250} />
    </ProCard>
  );
};

export default ProcessingEfficiency;

import { request } from '@umijs/max';

/**
 * 收入数据统计相关接口
 * 基础路径：/revenue-statistics
 */

/** 收入概览统计  GET /revenue-statistics/overview */
export async function overview(params?: {
  startDate?: string;
  endDate?: string;
}) {
  return request<API.ResType<API.RevenueOverviewStats>>(
    '/revenue-statistics/overview',
    {
      method: 'GET',
      params,
    },
  );
}

/** 收入趋势统计  GET /revenue-statistics/trend */
export async function trend(params: {
  startDate: string;
  endDate: string;
  periodType?: 'day' | 'week' | 'month';
}) {
  return request<API.ResType<API.RevenueTrendStats[]>>(
    '/revenue-statistics/trend',
    {
      method: 'GET',
      params,
    },
  );
}

/** 服务收入统计  GET /revenue-statistics/service */
export async function serviceStats(params?: {
  startDate?: string;
  endDate?: string;
  serviceTypeId?: number;
  serviceId?: number;
  page?: number;
  pageSize?: number;
  sortBy?: 'netRevenue' | 'totalOrderCount' | 'avgOrderValue';
  sortOrder?: 'asc' | 'desc';
}) {
  return request<API.ResType<API.PaginatedResponse<API.RevenueServiceStats>>>(
    '/revenue-statistics/service',
    {
      method: 'GET',
      params,
    },
  );
}

/** 员工收入统计  GET /revenue-statistics/employee */
export async function employeeStats(params?: {
  startDate?: string;
  endDate?: string;
  employeeId?: number;
  page?: number;
  pageSize?: number;
  sortBy?: 'netRevenue' | 'totalOrderCount' | 'avgOrderValue';
  sortOrder?: 'asc' | 'desc';
}) {
  return request<API.ResType<API.PaginatedResponse<API.RevenueEmployeeStats>>>(
    '/revenue-statistics/employee',
    {
      method: 'GET',
      params,
    },
  );
}

/** 收入构成分析  GET /revenue-statistics/composition */
export async function composition(params?: {
  startDate?: string;
  endDate?: string;
}) {
  return request<API.ResType<API.RevenueCompositionStats>>(
    '/revenue-statistics/composition',
    {
      method: 'GET',
      params,
    },
  );
}

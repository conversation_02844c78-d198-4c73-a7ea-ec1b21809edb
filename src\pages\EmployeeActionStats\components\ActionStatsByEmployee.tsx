import { employeeActionLogs } from '@/services';
import { UserOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Avatar, Space, Statistic, Tag, Typography, message } from 'antd';
import React, { useRef } from 'react';

const { Text } = Typography;

const ActionStatsByEmployee: React.FC = () => {
  const actionRef = useRef<ActionType>();

  const columns: ProColumns<API.EmployeeActionStatsByEmployee>[] = [
    {
      title: '时间范围',
      dataIndex: 'dateRange',
      key: 'dateRange',
      hideInTable: true,
      valueType: 'dateRange',
      search: {
        transform: (value) => {
          return {
            startDate: value[0],
            endDate: value[1],
          };
        },
      },
    },
    {
      title: '员工信息',
      dataIndex: 'employeeId',
      key: 'employeeId',
      width: 200,
      hideInSearch: true,
      render: (_, record) => (
        <Space>
          <Avatar icon={<UserOutlined />} size="small" />
          <div>
            <Text strong>{record.employeeName}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: 12 }}>
              {record.employeePhone}
            </Text>
          </div>
        </Space>
      ),
    },
    {
      title: '总动作数',
      dataIndex: 'totalActions',
      key: 'totalActions',
      width: 120,
      hideInSearch: true,
      render: (count: number) => (
        <Statistic
          value={count}
          valueStyle={{ fontSize: 16, color: '#1890ff' }}
        />
      ),
      sorter: true,
    },
    {
      title: '动作分解',
      dataIndex: 'actionBreakdown',
      key: 'actionBreakdown',
      hideInSearch: true,
      render: (breakdown: any[]) => (
        <Space wrap>
          {breakdown?.map((item) => (
            <Tag key={item.changeType} color="blue">
              {item.changeTypeLabel}: {item.count}
            </Tag>
          ))}
        </Space>
      ),
    },
  ];

  const expandedRowRender = (record: API.EmployeeActionStatsByEmployee) => {
    const expandColumns = [
      {
        title: '动作类型',
        dataIndex: 'changeTypeLabel',
        key: 'changeTypeLabel',
        width: 150,
      },
      {
        title: '执行次数',
        dataIndex: 'count',
        key: 'count',
        width: 120,
        render: (count: number) => (
          <Statistic value={count} valueStyle={{ fontSize: 14 }} />
        ),
      },
      {
        title: '占比',
        key: 'percentage',
        width: 100,
        render: (_: any, item: any) => {
          const percentage = record.totalActions
            ? ((item.count / record.totalActions) * 100).toFixed(1)
            : '0.0';
          return `${percentage}%`;
        },
      },
    ];

    return (
      <ProTable
        columns={expandColumns}
        dataSource={record.actionBreakdown}
        rowKey="changeType"
        pagination={false}
        search={false}
        toolBarRender={false}
        size="small"
        style={{ margin: '16px 0' }}
      />
    );
  };

  return (
    <ProTable<API.EmployeeActionStatsByEmployee>
      actionRef={actionRef}
      rowKey="employeeId"
      columns={columns}
      request={async (params) => {
        const { errCode, msg, data } = await employeeActionLogs.statsByEmployee(
          {
            ...params,
            startDate: params.dateRange?.[0],
            endDate: params.dateRange?.[1],
          },
        );
        if (errCode) {
          message.error(msg || '列表查询失败');
          return {
            data: [],
            total: 0,
          };
        }
        return {
          data: data?.list || [],
          total: data?.total || 0,
        };
      }}
      expandable={{
        expandedRowRender,
        rowExpandable: (record) => (record.actionBreakdown?.length || 0) > 0,
      }}
      scroll={{ x: '100%' }}
      search={{
        labelWidth: 'auto',
        defaultCollapsed: false,
        searchText: '查询',
        resetText: '重置',
        collapseRender: false,
        optionRender: (searchConfig, formProps, dom) => [...dom],
      }}
      form={{
        ignoreRules: false,
      }}
      pagination={{
        defaultPageSize: 20,
        showSizeChanger: true,
        showQuickJumper: true,
      }}
      dateFormatter="string"
      headerTitle="员工动作统计"
      toolBarRender={() => [
        <Text key="tip" type="secondary">
          点击行可展开查看详细动作分解
        </Text>,
      ]}
    />
  );
};

export default ActionStatsByEmployee;
